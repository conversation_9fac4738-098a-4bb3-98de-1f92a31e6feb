import {z} from 'zod';
import {tool} from "ai";
import {getProjectById} from '@/lib/db/project-queries';
import {Project} from "@/lib/db/schema";
import {ToolCountTracker} from './tool-count-tracker';
import {SupabaseContextEngine} from '@/lib/services/supabase-context-engine';

/**
 * Schema for the query Supabase context tool
 */
export const QuerySupabaseContextSchema = z.object({
    query: z.string().describe('The natural language query about Supabase resources. BEST QUERIES are specific, focused on particular functionality (e.g., "Show me tables related to user authentication" or "Find functions that handle payment processing" or "Show me the users table, the OPENAI secret, RLS Policies related to profile"). WORST QUERIES are vague or overly broad (e.g., "Show me all tables" or "What does this project do?"). Include specific entity names along with resources or functionality when possible.'),
    excludedResources: z.array(z.string()).optional().describe('Array of Supabase resources to exclude from the analysis in the format "resourceType.resourceName" (e.g., "table.users", "function.get_user"). You can use wildcards like "table.*" to exclude all tables.'),
    reason: z.string().describe('ANSWER all questions: Explain why you need this Supabase information. Is it absolutely necessary for the current task? Is the cost justified?'),
});

/**
 * Tool for querying Supabase resources using the context engine
 * This replaces the need for the bulky getSupabaseInstructions tool
 */
export const querySupabaseContext = ({
    projectId,
    messageId = '',
}: {
    projectId: string, 
    messageId?: string, 
}) => {
    return tool({
        description: 'Query Supabase resources to understand the project tables, dbFunctions, triggers, functions, RLS policies, storage buckets, and secrets. ' +
            'Use this instead of requesting full Supabase instructions. Issue queries that are as specific as possible' +
            'You can exclude specific resources using the excludedResources parameter in the format "resourceType.resourceName" or use wildcards like "table.*". ' +
            'This tool uses a two-stage approach to efficiently retrieve only the relevant Supabase resources. ' +
            'If the initial query returns incomplete information, the tool may indicate mustRetrieveAdditionalResources=true with guidance on what additional resources to request in a follow-up query.',
        parameters: QuerySupabaseContextSchema,
        execute: async ({query, excludedResources, reason}: z.infer<typeof QuerySupabaseContextSchema>) => {
            console.log('Supabase Query:', query);
            console.log('Excluded Resources:', excludedResources || []);
            console.log('Reason:', reason);

            if(!query) {
                return {
                    result: null,
                    message: "Query is required to search for Supabase resources."
                };
            }

            // Get the tool tracker instance
            const toolTracker = ToolCountTracker.getInstance();

            console.log('Tool call allowed (querySupabaseContext):', toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext'));

            // Check if we should allow this tool call
            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext')) {
                return {
                    result: null,
                    message: "⚠️ Tool call limit reached. You've already made multiple Supabase context queries. Please implement or summarize with the information you already have.",
                };
            }

            // Increment the tool count if we have a chat ID
            if (messageId) {
                toolTracker.incrementToolCount(messageId, 'querySupabaseContext');
            }

            try {
                // Get project information
                let project: Project | null = null;
                if (projectId) {
                    try {
                        project = await getProjectById({id: projectId});
                        if (!project) {
                            throw new Error(`Project not found: ${projectId}`);
                        }
                        
                        // Check if project is connected to Supabase
                        if (!project.connectionId || !project.supabaseProjectId) {
                            throw new Error('This project is not connected to Supabase');
                        }
                    } catch (error) {
                        console.error('Error fetching project:', error);
                        throw new Error(`Failed to fetch project information: ${error instanceof Error ? error.message : 'Unknown error'}`);
                    }
                } else {
                    throw new Error('Project ID is required');
                }

                // Initialize the Supabase context engine
                const contextEngine = new SupabaseContextEngine(project);

                // Query the Supabase context engine
                const result = await contextEngine.getRelevantSupabaseResources(query, excludedResources);

                // Prepare a more informative response
                const resourceCount = result.resources?.length || 0;
                let message = `Found ${resourceCount} relevant Supabase resources for your query.`;

                // Add information about resource types
                const resourceTypes = result.resources?.map(r => r.resourceType) || [];
                const uniqueTypes = [...new Set(resourceTypes)];
                
                if (uniqueTypes.length > 0) {
                    message += ` Types: ${uniqueTypes.join(', ')}.`;
                }
                
                // Add information about excluded resources if any
                if (excludedResources && excludedResources.length > 0) {
                    message += `\n${excludedResources.length} resources were excluded from the analysis.`;
                }
                
                // Add information about additional resources if needed
                if (result.hasAdditional) {
                    message += `\nThere are additional resources that may be relevant but weren't included due to the result limit.`;
                    
                    if (result.mustRetrieveAdditionalResources) {
                        message += `\n\n⚠️ IMPORTANT: You MUST make another query to retrieve critical additional resources. ${result.additionalResourcesSummary}`;
                    } else if (result.additionalResourcesSummary) {
                        message += `\n\nConsider querying for: ${result.additionalResourcesSummary}`;
                    }
                }

                return {
                    result,
                    message,
                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')
                };
            } catch (error) {
                console.error('Supabase context engine tool error:', error);
                throw new Error(`Failed to query Supabase context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        },
    });
};

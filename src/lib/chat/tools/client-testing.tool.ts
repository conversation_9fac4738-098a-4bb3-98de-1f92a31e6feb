import {DataStreamWriter, tool} from "ai";
import {z} from "zod";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";
import {TavilyService} from "@/lib/services/tavily";

/**
 * Tool to search the web for relevant information
 * This provides the AI with access to up-to-date information from the internet
 * Uses Tavily API for high-quality search results
 */
export const clientTestingTool = ({
                                      dataStream,
                                      creditUsageTracker
                                  }: {
    dataStream: DataStreamWriter;
    creditUsageTracker: CreditUsageTracker;
}) => {
    return tool({
        description: 'After every complex step, ask the user to test the functionality.',
        parameters: z.object({
            featuresToTest: z.string().describe("What should the user test? Be concise, noon-technical and focussed"),
            expectations: z.string().describe("What is the expected outcome?"),
            reason: z.string().describe("Why do you need the user to test the app right now?")
        })
    });
};

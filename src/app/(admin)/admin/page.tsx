import { redirect } from 'next/navigation';
import { auth } from '@/app/(auth)/auth';
import AdminDashboard from '@/components/admin/AdminDashboard';
import { getProjects, getAnalytics, getUsers, searchMessageContent } from '@/app/actions/admin-actions';

export default async function AdminPage() {
  // Auth guard - only allow specific email
  const session = await auth();
  
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    redirect('/');
  }
  
  // Pre-fetch initial data for all tabs on the server
  const initialProjectsData = await getProjects({
    page: 1,
    pageSize: 10,
    sortField: 'createdAt',
    sortDirection: 'desc'
  });
  
  const initialAnalyticsData = await getAnalytics();
  const initialUsersData = await getUsers({
    page: 1,
    pageSize: 10,
    sortField: 'createdAt',
    sortDirection: 'desc'
  });
  const initialMessagesData = await searchMessageContent('');

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>
      <AdminDashboard 
        initialProjectsData={initialProjectsData}
        initialAnalyticsData={initialAnalyticsData}
        initialUsersData={initialUsersData}
        initialMessagesData={initialMessagesData}
      />
    </div>
  );
}

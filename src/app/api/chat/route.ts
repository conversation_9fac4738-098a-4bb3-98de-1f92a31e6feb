import {
    type Message,
    convertToCoreMessages,
    createDataStreamResponse,
    generateText,
    streamObject,
    streamText,
    generateObject,
    CoreSystemMessage,
    TextPart,
    smoothStream, CoreMessage, ToolSet,
    TextStreamPart,
    CoreUserMessage, FinishReason, ImagePart, DataStreamWriter, CoreAssistantMessage, CoreToolMessage, createDataStream,
} from 'ai';
import {customModel} from "@/lib/ai";
import {DEFAULT_MODEL_NAME, models} from "@/lib/ai/models";
import {onboardingPrompt, systemPrompt} from '@/lib/ai/prompts';
import {FileItem, FileNode} from "@/types/file";
import {z} from 'zod';
import {
    deleteChatById,
    getChatById,
    getDocumentById,
    getLatestFileState,
    saveChat,
    saveFileState,
    saveFileStateAndCacheIfNeeded,
    markAsBaseCacheVersion,
    saveMessages,
    updateChat,
    createOrFetchAnonUser,
    getBaseCacheVersion,
    getMessagesSince,
    getMessagesByChatId,
    saveProject,
    updateProject,
    getMessageCountForToday,
    markChatAsInitialized, createStreamId,
    getStreamIdsByChatId
} from "@/lib/db/queries";
import { llmMediaService } from '@/lib/services/llm-media-service';
import {FileNodeV2} from "@/types/editor-v2";
import { FileLineManager } from '@/lib/editor/FileLineManager';
import { FileContentManager } from '@/lib/editor/FileContentManager';
import { checkMessageLimit } from '@/lib/subscription';
import { DEFAULT_DEPENDENCIES } from '@/types/editor';
import {generateTitleFromUserMessage} from "@/app/(generator)/generator/actions";
import {auth} from "@/app/(auth)/auth";
import {generateUUID, sanitizeResponseMessages} from "@/lib/utils";
import dayjs from "dayjs";
import {PlaceholderProcessingService} from "@/lib/services/PlaceholderProcessingService";
import {MOFileParser} from "@/lib/parser/StreamParser";
import {updateCreditUsage} from "@/lib/subscription/credit-usage";
import {MODiffParser} from "@/lib/parser/DiffParser";
import {saveTokenConsumption} from "@/lib/db/token-consumption.queries";
import {SupabaseIntegrationProvider} from "@/lib/integrations/supabase/SupabaseIntegrationProvider";
import {after, NextResponse} from "next/server";
import { MessageHandler } from '@/lib/chat/handlers';
import { createFileTool } from '@/lib/chat/tools/create-file.tool';
import {editFileTool} from "@/lib/chat/tools/edit-file.tool";
import {FileState, Project, Message as DBMessage, Chat} from "@/lib/db/schema";
import {enhancerPrompt} from "@/lib/services/prompt-enhancer";
import {ANTHROPIC_DEFAULT_MODEL} from "@/lib/ai/anthropic";
import { SupabaseArtifactHandler } from "@/lib/integrations/supabase/SupabaseArtifactHandler";

// Initialize the message handler

import {PerformanceTracker, performanceTracker} from '@/lib/utils/PerformanceTracker';
import {getProjectById, updateProjectMetadata} from "@/lib/db/project-queries";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import {LogEntry} from "@/types/logs";
import {getFileContents} from "@/lib/chat/tools/get-file-contents";
import {getSupabaseInstructions} from "@/lib/chat/tools/get-supabase-instructions";
import {getSupabaseLogs} from "@/lib/chat/tools/get-supabase-logs";
import {getClientLogs} from "@/lib/chat/tools/get-client-logs";
import {searchWeb} from "@/lib/chat/tools/search-web";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";
import {isErrorFixingMessage} from "@/lib/utils/error-detection";
import {TargetedValidator, ValidationResult} from "@/lib/services/targeted-validator";
import {ComponentContext} from "@/types/component-context";
import {addAiMemory} from "@/lib/chat/tools/add-ai-memory";
import {StreamService} from "@/lib/services/stream-service";
import {queryCodebase} from "@/lib/chat/tools/query-codebase";
import {ToolCountTracker} from "@/lib/chat/tools/tool-count-tracker";
import {exportData} from "@/lib/server-utils";
import {boolean, uuid} from "drizzle-orm/pg-core";
import {
    createResumableStreamContext,
    type ResumableStreamContext,
} from 'resumable-stream'
import { differenceInSeconds } from 'date-fns';
import {getScreenshots} from "@/lib/chat/tools/get-screenshots";
import {generateReactNativeScreens} from "@/lib/chat/tools/generate-rn-screen";
import {manageSupabaseAuth} from "@/lib/chat/tools/manage-supabase-auth";
import {clientTestingTool} from "@/lib/chat/tools/client-testing.tool";
import { querySupabaseContext } from '@/lib/chat/tools/query-supabase-context';

const runLintAndFixErrors = async (
    validationResult: {
        isValid: boolean;
        fileResults: Record<string, ValidationResult>;
        summary: string
    }, placeholderSubstitutedFiles: Awaited<FileNode>[],
    fileManager: FileLineManager,
    dataStream: DataStreamWriter,
    creditUsageTracker: CreditUsageTracker) => {
    const filesWithErrors: Awaited<FileNode>[] = [];
    for (const [fileName, result] of Object.entries(validationResult.fileResults)) {
        if (!result.isValid) {
            console.log(`File with errors: ${fileName}`);
            // Find the original file with this name
            const originalFile = placeholderSubstitutedFiles.find(f => f.name.endsWith(fileName));
            if (originalFile) {
                filesWithErrors.push(originalFile);
            }
        }
    }

    if (filesWithErrors.length > 0) {
        console.log(`Attempting AI correction for ${filesWithErrors.length} files with syntax errors...`);

        // Process all files with errors in one batch
        try {
            // Prepare a batch of files for correction
            const filesToCorrect = filesWithErrors.map(file => {
                const fileName = file.name.split('/').pop() || 'file.ts';
                return {
                    name: fileName,
                    content: file.content,
                    originalPath: file.name
                };
            });

            // Build a comprehensive prompt for all files
            let prompt = `Fix the syntax errors in the following code files. For each file, return ONLY the corrected code with no explanations or markdown. Consider the fact the typescript union type and typeof with object values (typeof musicItems[0]) do not work. Safely replace these types with 'any' to fix the type issues.\n\n`;

            for (const file of filesToCorrect) {
                prompt += `FILE: ${file.name}\n\n${file.content}\n\n---END OF FILE---\n\n`;
            }

            prompt += `\nFor each file, return the corrected version in this format:\n`;
            prompt += `FILE: [filename]\n[corrected code]\n---END OF FILE---\n`;

            const result = await generateText({
                model: customModel('openai/gpt-4.1-nano'),
                messages: [
                    {
                        role: 'system',
                        content: 'You are a precise code correction assistant. Your only job is to fix syntax errors in code files. Return the corrected files in the exact format requested, with no additional text or explanations.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1
            });

            // Parse the response to extract corrected files
            const correctedText = result.text.trim();
            const fileSegments = correctedText.split('---END OF FILE---').filter(segment => segment.trim().length > 0);

            console.log(`AI returned ${fileSegments.length} corrected files`);

            // Process each corrected file
            for (const segment of fileSegments) {
                const fileMatch = segment.match(/FILE:\s*([^\n]+)\n([\s\S]+)/i);

                if (fileMatch && fileMatch.length >= 3) {
                    const fileName = fileMatch[1].trim();
                    let correctedCode = fileMatch[2].trim();

                    // Find the original file info
                    const originalFile = filesToCorrect.find(f => f.name === fileName || f.name.endsWith(fileName));

                    if (originalFile) {
                        // Validate the corrected code
                        const validationResult = TargetedValidator.validateFiles([{
                            name: fileName,
                            content: correctedCode
                        }]);

                        if (validationResult.isValid) {
                            console.log(`Successfully fixed syntax errors in ${originalFile.originalPath}`);

                            // Update in the file manager
                            fileManager.replaceFile(originalFile.originalPath, correctedCode);

                            // Update the original file object for saving later
                            const fileToUpdate = filesWithErrors.find(f => f.name === originalFile.originalPath);
                            if (fileToUpdate) {
                                fileToUpdate.content = correctedCode;
                            }

                            dataStream.writeData({
                                type: 'status',
                                content: `Fixed syntax errors in ${fileName}`
                            });

                            // Track this validation fix as an error fixing operation (exempt from credits)
                            creditUsageTracker.trackDiscountedOperation('file_change', 'auto_fix', 1);

                            dataStream.writeData({
                                type: 'file-operation',
                                content: {
                                    type: 'edit',
                                    absolutePath: originalFile.originalPath,
                                    content: correctedCode,
                                    usedAICorrection: true
                                }
                            });
                        } else {
                            console.error(`Failed to fix syntax errors in ${originalFile.originalPath}`);
                        }
                    } else {
                        console.error(`Could not match corrected file ${fileName} to original files`);
                    }
                }
            }
        } catch (error) {
            console.error(`Error in batch AI correction:`, error);
            dataStream.writeData({
                type: 'status',
                content: `Error fixing syntax errors: ${error instanceof Error ? error.message : String(error)}`
            });
        }
    }
}

let globalStreamContext: ResumableStreamContext | null = null;

function getStreamContext() {
    if (!globalStreamContext) {
        try {
            globalStreamContext = createResumableStreamContext({
                waitUntil: after,
            });
        } catch (error: any) {
            if (error.message.includes('REDIS_URL')) {
                console.log(
                    ' > Resumable streams are disabled due to missing REDIS_URL',
                );
            } else {
                console.error(error);
            }
        }
    }

    return globalStreamContext;
}

/**
 * Detect critical issues in logs that the LLM should be aware of
 */
function detectCriticalIssuesInLogs(logs: LogEntry[]): string[] {
  const criticalIssues: string[] = [];
  
  // Get recent logs (last 5 minutes)
  const recentLogs = logs.filter(log => 
    (Date.now() - log.timestamp) < 5 * 60 * 1000
  );
  
  // Check for error patterns
  const errorLogs = recentLogs.filter(log => log.type.toLowerCase() === 'error');
  if (errorLogs.length > 3) {
    criticalIssues.push(`Multiple errors detected (${errorLogs.length} in the last 5 minutes)`);
  }
  
  // Check for network errors
  const networkErrors = recentLogs.filter(log => 
    log.source === 'network' && 
    (log.message.includes('status: 5') || log.message.includes('failed') || log.message.includes('error'))
  );
  if (networkErrors.length > 0) {
    criticalIssues.push(`Network errors detected (${networkErrors.length} in the last 5 minutes)`);
  }
  
  // Check for authentication issues
  const authIssues = recentLogs.filter(log => 
    log.message.toLowerCase().includes('auth') && 
    (log.message.toLowerCase().includes('error') || log.message.toLowerCase().includes('failed') || 
     log.message.toLowerCase().includes('unauthorized') || log.message.toLowerCase().includes('forbidden'))
  );
  if (authIssues.length > 0) {
    criticalIssues.push(`Authentication issues detected (${authIssues.length} in the last 5 minutes)`);
  }
  
  return criticalIssues;
}

export async function POST(request: Request) {
    // Start tracking the overall API request time
    const requestId = `chat-api-${Date.now()}`;
    performanceTracker.startTimer(requestId, 'Chat API Request', { endpoint: 'POST /api/chat' });

    // Check if this is an auto-fixed message

    let {
        id,
        messages,
        files,
        activeFile,
        dependencies,
        linkSupabaseProjectId,
        linkSupabaseConnection,
        projectId,
        logs,
        agentModeEnabled,
        isReload,
        isInitial,
        componentContexts,
        isAutoFixed
    }: {
        id: string;
        messages: Array<Message>,
        files: FileItem[],
        activeFile: string,
        dependencies?: Record<string, any>,
        linkSupabaseProjectId: string,
        linkSupabaseConnection: string,
        projectId: string;
        logs: LogEntry[],
        agentModeEnabled: boolean,
        isReload: boolean;
        isInitial?: boolean;
        isAutoFixed?: boolean,
        componentContexts?: Array<ComponentContext>
    } =
        await request.json();


    // console.log('messages', messages, id, files, activeFile)

    // const apiModelId = DEFAULT_MODEL_NAME;

    // const model = models.find(model => model.id === apiModelId);
    //
    // if (!model) {
    //     return new Response("Model not found", {status: 400});
    // }


    // Track auth time
    const authTimerId = `${requestId}-auth`;
    performanceTracker.startTimer(authTimerId, 'Authentication', { requestId }, requestId);
    const session = await auth();
    performanceTracker.stopTimer(authTimerId);

    const anonymousId = request.headers.get('x-anonymous-id');
    let chatCount = 0;
    let isAnonymous = false;


    let userId = session?.user?.id;

    // Handle anonymous users
    if (!userId && anonymousId) {
        try {
            const result = await Promise.all([
                createOrFetchAnonUser(anonymousId),
                // getMessageCountForToday(anonymousId)
            ]);
            const anonUser = result[0]
            // chatCount = result[1];
            userId = anonUser.id;
            isAnonymous = true;
        } catch (error) {
            console.error('Failed to create/fetch anonymous user:', error);
            return new Response('Failed to create anonymous session', { status: 500 });
        }
    }

    // Ensure we have a valid userId
    if (!userId) {
        return new Response('Unauthorized - No valid user ID', { status: 401 });
    }

    // // Check chat count for anonymous users
    // if (isAnonymous && (!chatCount || chatCount >= 3)) {
    //     return NextResponse.json({
    //         error: 'Chat limit reached',
    //         limit: 5,
    //         remaining: 0,
    //         isPro: false,
    //         isAnonymous: true,
    //     }, { status: 429 });
    // }
    //
    // // Check anonymous chat limit
    // if (!session?.user && chatCount >= 3) {
    //     return NextResponse.json({
    //             error: 'Chat limit reached',
    //             limit: 5,
    //             remaining: 0,
    //             isPro: false,
    //             isAnonymous: true,
    //         }, { status: 429 });
    // }

    // Check message limits
    const limitCheckTimerId = `${requestId}-limit-check`;
    performanceTracker.startTimer(limitCheckTimerId, 'Message Limit Check', { requestId, userId }, requestId);
    const messageCheck = await checkMessageLimit(userId!, isAnonymous);

    performanceTracker.stopTimer(limitCheckTimerId);
    if (!messageCheck.canSendMessage) {
        return new Response(JSON.stringify({
            error: 'Message limit reached',
            limit: messageCheck.limit,
            remaining: messageCheck.remaining,
            isPro: messageCheck.isPro,
            isAnonymous: isAnonymous
        }), {
            status: 429,
            headers: { 'Content-Type': 'application/json' }
        });
    }

    const messageHandler = new MessageHandler();

    let isFirstMessage = false;

    const chatLookupTimerId = `${requestId}-chat-lookup`;
    performanceTracker.startTimer(chatLookupTimerId, 'Chat Lookup', { requestId, chatId: id }, requestId);
    let [chat, project] = await Promise.all([
        getChatById({ id }),
        projectId ? getProjectById({id:projectId}) : null
    ]);
    performanceTracker.stopTimer(chatLookupTimerId);

    let isChatForExistingProject = false;
    if (!project) {
        const {
            appName,
            packageName,
            description,
            primaryColor,
            bundleIdentifier
        } = await generateProjectAttributes({messages: messages})
        // Generate a new project
        project = await saveProject({
            id: generateUUID(),
            userId,
            appName,
            packageName,
            description,
            primaryColor,
            bundleIdentifier,
            isMigratedv1: true
        })
        projectId = project.id;
    } else if (project && !project.isMigratedv1) {
        const metadata = await generateProjectAttributes({messages: messages})
        project = await updateProjectMetadata(projectId, {...metadata, isMigratedv1: true});
    } else {
        project = await updateProject({id: projectId, updatedAt: new Date()});
        isChatForExistingProject = true;
    }

    if(!project) {
        return new Response('Project not found', {status: 404});
    }

    // If the chat is for an existing project, we don't need to setup the first message guidelines. AI can continue doing its thing.
    if(!isChatForExistingProject) {
        // The definite first message is that the project is new and does not contain any assistant messages
        isFirstMessage = !messages.find(message => {
            return message.role === "assistant" && !message.parts?.some(part => part.type === 'tool-invocation');
        });

        // If this is the first message, we should disable the agent mode and allow creation of project as is.
        if (isFirstMessage) {
            agentModeEnabled = false;
        }
    }

    if(!isFirstMessage) {
        agentModeEnabled = true;
    }

    const TOTAL_MESSAGES_FOR_TURNS_AND_CACHE = agentModeEnabled ? 20 : 5; // Increased from 5 to provide more context for the agent

    // Process messages using the message handler
    const messageInitTimerId = `${requestId}-message-init`;
    performanceTracker.startTimer(messageInitTimerId, 'Message Handler Initialization', { requestId }, requestId);
    await messageHandler.initialize(messages, TOTAL_MESSAGES_FOR_TURNS_AND_CACHE, {
        projectId,
        backendEnabled: session?.user?.email === "<EMAIL>",
        componentContexts: componentContexts,
        isFirstUserMessage: isFirstMessage,
        agentModeEnabled: agentModeEnabled,
        userId
    });
    performanceTracker.stopTimer(messageInitTimerId);
    const userMessage = messageHandler.getCurrentUserMessage();

    if (!userMessage) {
        return new Response('No user message found', {status: 400});
    }

    const agentModeWorking = messages.some(m => {
        return m.toolInvocations?.some(tool => {
            return tool.toolName === "editFile"
        })
    });
    if(!agentModeWorking && agentModeEnabled) {
        // Force switch once to agent mode.
        messageHandler.enhanceUserMessage(`
<hidden>
 [CRITICAL]: You are pattern matching.          Please use the editFile and queryCodebase tools (not inline tags) using the correct tool calling and not inline tags. 
            MO_ tags are deprecated.
            
            // This is the CORRECT way to use editFile/queryCodebase etc - DO NOT use MO_FILE tags! Use the editFile/queryCodebase etc tools you have available
editFile({
  absolutePath: '/path/to/file.tsx',
  edits: [
    {
      searchPattern: 'import React from \'react\';\nimport { View } from \'react-native\';',
      replacementContent: 'import React from \'react\';\nimport { View, Text } from \'react-native\';',
      description: 'Adding Text import'
    },
    {
      searchPattern: '  return (\n    <View style={styles.container}>',
      replacementContent: '  return (\n    <View style={styles.container}>\n      <Text>Hello World</Text>',
      description: 'Adding Text component'
    }
  ]
});

## INCORRECT EXAMPLES (DO NOT Use These Patterns):

// NEVER use this deprecated syntax - it will BREAK the application
// <MO_FILE lang="typescript" path="/path/to/file.tsx">
// File content here
// </MO_FILE>

// This is also wrong. It will break the app.
<editFile> <absolutePath>screens/DataExportScreen.tsx</absolutePath> <edits> <edit> <searchPattern></searchPattern> <replacementContent>import React, { useState } from 'react'; import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native'; import { Download, FileText, Database, Calendar } from 'lucide-react-native'; import { Header } from '../components/Header'; import { Card } from '../components/Card'; import { Button } from '../components/Button'; import { COLORS } from '../constants/colors'; import { PermissionGate } from '../components/PermissionGate'; import { Permission } from '../contexts/AuthContext'; import { logger } from '../utils/logger';
    </hidden>`)
    }


    const chatSaveTimerId = `${requestId}-chat-save`;
    performanceTracker.startTimer(chatSaveTimerId, 'Chat Save/Update', { requestId, chatId: id, isNew: !chat }, requestId);


    if (!chat) {
        const title = await generateTitleFromUserMessage({message: userMessage as CoreUserMessage});
        chat = await saveChat({id, userId, title, updatedAt: new Date(), projectId});
    } else {
        chat = await updateChat({id, updatedAt: new Date(), isInitialized: true});
    }
    performanceTracker.stopTimer(chatSaveTimerId);

    // If the user has hit the reload button, the active user message is the last message as it is the only allowed to be retried
    const userMessageId = messages.findLast(message => message.role === 'user')?.id || generateUUID();
    const creditUsageTracker = new CreditUsageTracker();

    const messageSaveTimerId = `${requestId}-message-save`;
    performanceTracker.startTimer(messageSaveTimerId, 'Message Save', { requestId, messageId: userMessageId }, requestId);
    if(logs && logs.length) {
        // Check for critical issues in logs
        // const criticalIssues = detectCriticalIssuesInLogs(logs);
        
//         if (criticalIssues.length > 0) {
//             // Only add a nudge about critical issues instead of all logs
//             const logNudgeMessage = `
// <hidden>
// There appear to be some issues in the client logs that might be relevant to debugging:
// ${criticalIssues.map(issue => `- ${issue}`).join('\n')}
//
// You can use the getClientLogs tool to view the most recent logs and investigate further.
// [NOTE]: Client logs only update after user interaction with the app, not during LLM response generation. Means even if you call this tool a bunch of times, you will get the same result. Think carefully.
// </hidden>`;
//
//             messageHandler.enhanceUserMessage(logNudgeMessage);
//         }
    }

    if(!isReload) {
        await saveMessages({
            messages: [
                messageHandler.createMessageForSaving(messageHandler.getCurrentUserMessage()!, userMessageId, id, userId, !!isAutoFixed)
            ],
        });
    }

    performanceTracker.stopTimer(messageSaveTimerId);

    if (!chat) {
        // Save initial state of the files and mark as base cache version
        const fileStateSaveTimerId = `${requestId}-file-state-save`;
        performanceTracker.startTimer(fileStateSaveTimerId, 'File State Save', { requestId, chatId: id }, requestId);
        await saveFileStateAndCacheIfNeeded({
            chatId: id,
            messageId: userMessageId,
            files,
            dependencies: dependencies || DEFAULT_DEPENDENCIES,
            shouldCache: true // Always mark the first state as the base cache version
        });
        performanceTracker.stopTimer(fileStateSaveTimerId);
    }





    const aiResponseTimerId = `${requestId}-ai-response`;
    performanceTracker.startTimer(aiResponseTimerId, 'AI Response Generation', { requestId }, requestId);

    // Add component contexts to the system message if they exist
    if (componentContexts && componentContexts.length > 0) {
        const componentContextsInfo = componentContexts.map(context => {
            return `Component: ${context.componentName} (${context.element})
Source File: ${context.sourceFile}
Line Number: ${context.lineNumber}`;
        }).join('\n\n');

        messageHandler.enhanceUserMessage(`\n\nThe user has shared the following component contexts from their app:\n\n${componentContextsInfo}\n\nPlease consider these components when providing assistance but don't get confused by any incorrect contexts. If unsure, just ask.
        Note about component screenshots: The images you're viewing are captured using a web-based screenshot method that may introduce visual artifacts not present in the actual interface. These artifacts commonly include:
1. Yellowish/amber borders or outlines around elements
2. Color shifts where backgrounds may appear tinted (often toward brown/amber tones)
3. Inconsistent rendering of transparency
4. Subtle edge artifacts where elements meet

When interpreting these screenshots, please focus on the content, layout, and relative positioning rather than exact color fidelity or border precision. The actual UI may have cleaner edges, different background colors, or more subtle transitions than what appears in these screenshots. These discrepancies are technical limitations of the capture process, not design choices."`);
    }

    // console.log('userMessage', messageHandler.getCurrentUserMessage())

    return createDataStreamResponse({
        execute: async (dataStream) => {
            // Send the user message ID first
            dataStream.writeData({
                type: 'user-message-id',
                content: userMessageId,
            });

            if(logs && logs.length) {
                dataStream.writeData({
                    type: 'update-user-message',
                    content: {userMessage: messageHandler.getCurrentUserMessageForUI().content}
                })
            }

            let enhancedRequirements = '';
            // let initialPromptGuidelines: string = '';
            // if(isFirstMessage) {
            //     const enhanceReqTimerId = `${requestId}-enhance-requirements`;
            //     initialPromptGuidelines = `The user hates seeing "X is not defined" error. Make sure to import all the properties from styles BORDER_RADIUS, SHADOW, COLORS, SPACING, TYPOGRAPHY, FONTS. NOT doing so will result in the user getting angry and leave.`;
            //     performanceTracker.startTimer(enhanceReqTimerId, 'Enhance Requirements', { requestId }, requestId);
            //     console.time('enhancedRequirements')
            //     // enhancedRequirements = await enhancerPrompt(userMessage, initialPromptGuidelines);
            //     // messageHandler.enhanceUserMessage(`\n<INTERNAL_UX_DESIGNER_GUIDELINES>${enhancedRequirements}</INTERNAL_UX_DESIGNER_GUIDELINES>\n${initialPromptGuidelines}`);
            //     console.timeEnd('enhancedRequirements')
            //     performanceTracker.stopTimer(enhanceReqTimerId);
            //     console.log(enhancedRequirements)
            // }


            const supabaseTimerId = `${requestId}-supabase-integration`;
            performanceTracker.startTimer(supabaseTimerId, 'Supabase Integration', { requestId }, requestId);
            const supabaseIntegrationProvider = new SupabaseIntegrationProvider();

            let supabasePrompt: string | undefined;
            let isSupabaseConnected =  !!((project?.supabaseProjectId && project?.connectionId) && !linkSupabaseProjectId);
            let isConnectingSupabase = false;
            if (!!linkSupabaseProjectId || (project?.supabaseProjectId && project?.connectionId)) {
                if (session?.user?.id) {
                    let instructions;
                    if (linkSupabaseProjectId) {
                        isConnectingSupabase = true;
                        const result = await Promise.all([
                            await supabaseIntegrationProvider.getProjectData({
                                projectRef: linkSupabaseProjectId,
                                userId: session?.user?.id
                            }),
                            linkSupabaseProjectId ? await supabaseIntegrationProvider.linkChatToProject({
                                chatId: id,
                                connectionId: linkSupabaseConnection,
                                providerProjectId: linkSupabaseProjectId,
                                projectId
                            }) : Promise.resolve(null)
                        ])
                        isSupabaseConnected = true;
                        instructions = result[0].instructions;
                        project.supabaseProjectId = linkSupabaseProjectId;
                        project.connectionId = linkSupabaseConnection;
                    } else if(!agentModeEnabled) {
                        // Moved to a separate tool call for agent mode
                        const result = await supabaseIntegrationProvider.getLatestInstructionsForChat({project})
                        instructions = result.instructions;
                    } else {
                        instructions = supabaseIntegrationProvider.getSupabaseInitialInstructions();
                    }
                    supabasePrompt = instructions;
                }
            }
            performanceTracker.stopTimer(supabaseTimerId);

            if(!isFirstMessage) {
                messageHandler.appendToSystemPrompt(
                    !isSupabaseConnected  ?
                        `<SUPABASE_STATUS>NOT CONNECTED. Please suggest the action<action type="tool" tool="supabase_integration">Connect Supabase</action> within an <actions></actions> block for the user to connect supabase and explicitly mention it in your response.</SUPABASE_STATUS>`:
                        '<SUPABASE_STATUS>Supabase is connected. Please use the getSupabaseInstructions tool to fetch the details about database, schema, triggers, functions, edge functions, secrets etc. Look at <SUPABASE_INSTRUCTIONS></SUPABASE_INSTRUCTIONS> very very carefully. </SUPABASE_STATUS>'
                )
                if(!agentModeEnabled || linkSupabaseProjectId) {
                    messageHandler.enhanceUserMessage(supabasePrompt);
                }
            }

            if(session?.user.name) {
                messageHandler.appendToSystemPrompt(
                    `The user's name is ${session?.user.name}. Make sure to use their first name when addressing them. Their project ID is ${project?.id}. Use this project id in all the storage keys and other keys to keep it unique.\n`
                )
            }

            // Get the updated user message from the handler
            // const updatedUserMessage = messageHandler.getCurrentUserMessage();
            // console.log('updatedUserMessage', updatedUserMessage) begigging

            if(agentModeEnabled && !isFirstMessage) {
//                 messageHandler.enhanceUserMessage(`
//     <extended_system_prompt>
//     Please don't get stuck in a tool calling loop. Also, calling getFileContents is not mandatory if you already have the context of the file you are supposed to make changes to.
//     Make HOLISTIC changes thinking about every single file to ensure continuity of the project without breaking existing functionality include but not limited to the design, theming, flow, backend, architecture etc.
//     [Critical, nearly, life-threatening]: Before calling any tool, you MUST output your plan in <thinking></thinking>.
//
//     ALWAYS use <MO_FILE path="absolute path without / prefix at the beginning" approx_lines="Approximate line count number of the file"></MO_FILE> and <MO_DIFF path="absolute path without / prefix at the beginning"></MO_DIFF> to make changes.
//     ALWAYS try to batch the get_file_contents to fetch similar/related files to save costs.
//     ALWAYS play close attention to warnings and ensure each file is not more than 300 lines long.
//
// You now have access to a powerful context engine tool called 'queryCodebase'. Use this tool FIRST before any other tools to understand the codebase structure and relationships.
//
// Context Engine Usage:
// 1. Call queryCodebase with a specific question about what you need to understand
// 2. The tool will return relevant files and their relationships in a single call
// 3. Use this information to plan your changes without requesting individual files
//
// After using the context engine, make edits using MO_FILE or MO_DIFF or write SQL queries using MO_DATABASE_QUERY.
// Please don't get stuck in a tool calling loop. The context engine eliminates the need for multiple getFileContents calls.
// Please make sure to make holistic and coherent changes all at once. If fixing bugs, use the context engine to find all relevant files and fix them all at once.
//
//     <recovery_instructions>
//     If you notice that you've broken a feature or functionality:
//     1. Don't panic - you can fix it
//     2. Look at the file summaries in previous messages to understand what files you modified
//     3. Use queryCodebase to get a comprehensive view of the codebase structure
//     4. Fetch the specific files you need to fix using getFileContents
//     5. Make targeted changes to restore functionality without breaking other features
//     6. Test your changes by asking the user to try the feature again
//     </recovery_instructions>
//
//     Hint: Think holistically, plan, populate context, understand, make changes, verify, repeat if needed.
//     Forget files from previous context. Start afresh and think holistically, plan holistically and don't implement till you have the full picture of the requirements.
//     Never change the existing design of the app. If unsure of what a screen/component/file looks like, always fetch it before making changes.
//     NO MATTER WHAT, DO NOT ALTER THE FUNCTIONALITY or the DESIGN.
//     </extended_system_prompt>
//     `);
            }


            // const latestFileState = await getLatestFileState(id);
            // const updatedFiles = latestFileState.files as FileItem[];
            // const updatedDependencies = latestFileState.dependencies;
            // Initialize file manager with all files

            let baseCacheVersion: FileState | null = null;
            if(!agentModeEnabled) {
                // Check if we have a base cache version
                baseCacheVersion = await getBaseCacheVersion(id);

                if(baseCacheVersion) {
                    console.log(`---- Using base cache version for ${id} ----`, baseCacheVersion?.version);
                } else {
                    console.log('---- Using UI version for ${id} ----');
                }
            }

            const fileManager = new FileLineManager();
            fileManager.initializeFiles(files);

            // Initialize content manager for diff operations
            const contentManager = new FileContentManager();

            // Initialize files in content manager from the file manager
            files.forEach(file => {
                const content = fileManager.getFinalContent(file.name);
                if (content) {
                    contentManager.setFileContent(file.name, content);
                }
            });

            try {


                // Keep track of message count since base version
                let messageCount = 0;
                if (baseCacheVersion) {
                    // Extremely slow. Need to solve this.
                    // Get messages since base version using our improved function
                    const messagesSinceBase = await getMessagesSince(id, new Date(baseCacheVersion.createdAt));
                    // console.log('Messages since base version:', messagesSinceBase.length);
                    messageCount = messagesSinceBase.length;
                }

                // Decide whether to use cache or create a new base
                const shouldCreateNewBase = !baseCacheVersion || messageCount > TOTAL_MESSAGES_FOR_TURNS_AND_CACHE;

                // Add image handling instructions with appropriate caching
                messageHandler.createFileMessage((baseCacheVersion?.files as FileNode[] || files), fileManager, !shouldCreateNewBase, agentModeEnabled);

                // Process the response to replace image placeholders
                const placeholderService = new PlaceholderProcessingService(llmMediaService);

                const processImagePlaceholders = async (text: string) => {
                    const { processedText, metrics } = await placeholderService.processImagePlaceholders(text);
                    return processedText;
                };

                const processVideoPlaceholders = async (text: string) => {
                    const { processedText, metrics } = await placeholderService.processVideoPlaceholders(text);
                    return processedText;
                };

                const {isErrorFixing, errorUuid} = isErrorFixingMessage(userMessage.content);


                let startedStreaming = false;

                const circuitBreakerState: {
                    params: {
                        fileRequestCounter: number,
                        requestedFilesSet: Set<string>,
                        consecutiveRequestsWithoutAction: number
                    },
                    resetCircuitBreaker: () => void
                } = {
                    params: {
                        fileRequestCounter: 0,
                        requestedFilesSet: new Set<string>(),
                        consecutiveRequestsWithoutAction: 0
                    },
                    resetCircuitBreaker() {
                        this.params.fileRequestCounter = 0;
                        this.params.consecutiveRequestsWithoutAction = 0;
                    }
                };

                const parser = new MOFileParser({
                    onFileStart: (meta) => {
                        startedStreaming = true;
                        console.log('File started:', meta.path);
                    },
                    onFileDelta: (path, chunk, file) => {
                        // console.log('File update:', path, chunk.length, file?.total_lines);
                    },
                    onFileComplete: async (meta) => {
                        fileManager.replaceFile(meta.path, meta.content);
                        // Also update the content manager
                        contentManager.setFileContent(meta.path, meta.content);

                        let content = meta.content;
                        content = await processImagePlaceholders(content);
                        content = await processVideoPlaceholders(content);

                        // Check if user message contains error fixing criteria using our utility

                        // Only track credit usage if not fixing errors
                        if (!isErrorFixing) {
                            creditUsageTracker.trackOperation('code_write');
                        } else {
                            creditUsageTracker.trackDiscountedOperation('file_change', 'error_fixing', 1);
                        }

                        circuitBreakerState.resetCircuitBreaker();
                        dataStream.writeData({
                            type: 'file-operation',
                            content: {
                                type: 'create',
                                absolutePath: meta.path,
                                content
                            }
                        });
                    }
                });

                // Create the diff parser to handle MO_DIFF tags
                const diffParser = new MODiffParser({
                    onDiffStart: (meta) => {
                        startedStreaming = true;
                        console.log('Diff started:', meta.path);
                    },
                    onDiffComplete: async (meta) => {
                        console.log('Diff completed:', meta.path);

                        // Apply the diff to the file content
                        const result = await contentManager.applyDiff(meta, { bestEffort: true, filePath: meta.path });

                        // console.log('result', meta.path, result)
                        // If the result includes credit usage from AICompletionStrategy, merge it with our tracker
                        if (result.creditUsage) {
                            // Get discounted operations from the AICompletionStrategy
                            const discountedOps = result.creditUsage.getDiscountedOperations();

                            // Add them to our main tracker
                            for (const op of discountedOps) {
                                creditUsageTracker.trackDiscountedOperation(op.type, op.reason, op.count);

                                // Send notification to client about free error fixing
                                // if (op.reason === 'error_fixing') {
                                //     dataStream.writeData({
                                //         type: 'credit-usage-event',
                                //         content: {
                                //             type: 'error_fixing',
                                //             message: 'Error fixing doesn\'t consume credits! Keep your app running smoothly.'
                                //         }
                                //     });
                                // }
                            }
                        }
                        if (!result.success) {
                            console.error('Failed to apply diff:', result.message);
                            return;
                        }

                        if (result.message) {
                            console.warn('Diff applied with warnings:', result.message);
                        }

                        // Update the line manager with the new content
                        fileManager.replaceFile(meta.path, result.content as string);

                        // Process placeholders and stream the updated file
                        let content = result.content;
                        content = await processImagePlaceholders(content as string);
                        content = await processVideoPlaceholders(content)


                        // Only track credit usage if not fixing errors
                        if (!isErrorFixing) {
                            creditUsageTracker.trackOperation('file_change');
                        } else {
                            creditUsageTracker.trackDiscountedOperation('file_change', 'error_fixing', 1);
                        }

                        circuitBreakerState.resetCircuitBreaker();
                        dataStream.writeData({
                            type: 'file-operation',
                            content: {
                                type: 'edit',
                                absolutePath: meta.path,
                                content
                            }
                        });
                    },
                    onDiffError: (error, path) => {
                        console.error('Diff error:', error, path ? `in ${path}` : '');

                        // Optionally notify the client about the error
                        dataStream.writeData({
                            type: 'diff-error',
                            content: {
                                path: path || 'unknown',
                                error
                            }
                        });
                    }
                });

                // const validationResults = TargetedValidator.validateFiles(files);
                // if(!validationResults.isValid) {
                //     messageHandler.enhanceUserMessage(`
                //     <PROJECT_LINT_RESULT>${validationResults.summary}</PROJECT_LINT_RESULT>
                //     Please fix these issues as well silently without telling the user.
                //     `)
                // }

                if(project.aiGeneratedMemory) {
                    messageHandler.enhanceUserMessage(`These are the points you added using the addAiMemory tool earlier for you to remember about the project/user. \n ${project.aiGeneratedMemory}`)
                }

                const enabledTools = [
                    'aiAddMemory',
                    // 'generateReactNativeScreens',
                    'clientTesting'
                ];
                if (agentModeEnabled) {
                    if (!isFirstMessage) {
                        // enabledTools.push('getFileContents')
                        enabledTools.push('queryCodebase')
                        enabledTools.push('searchWeb')
                        enabledTools.push('editFile')
                        enabledTools.push('getClientLogs')
                        enabledTools.push('getScreenshots')
                        if (isSupabaseConnected) {
                            enabledTools.push('getSupabaseInstructions')
                            enabledTools.push('getSupabaseLogs')
                            enabledTools.push('manageSupabaseAuth')
                            enabledTools.push('querySupabaseContext')
                        }
                    }
                }
                console.log('enabledTools ---->', enabledTools)

                // console.log('userMessage.content', userMessage.content)

                // Create a new tool tracker for this request
                const toolTracker = ToolCountTracker.getInstance();

                const dynamicResult = await generateObject({
                    system: `You are a temperature optimization expert for LLM responses. Analyze the conversation context and determine the optimal temperature for the next AI response.

TEMPERATURE PHILOSOPHY:
- 0.0-0.2: Deterministic, factual, following established patterns
- 0.3-0.5: Balanced creativity with structure  
- 0.6-0.8: Creative problem-solving and exploration
- 0.9-1.0: Maximum creativity for breakthrough thinking

WHEN TO USE LOW TEMPERATURE (0.0-0.3):
- User asks for factual information
- Following clear instructions or templates
- Repeating successful patterns
- User wants consistency and reliability
- Technical documentation or specifications

WHEN TO USE MEDIUM TEMPERATURE (0.3-0.6):
- Moderate problem complexity
- Some interpretation required
- Balancing creativity with accuracy
- Standard problem-solving tasks
- User requests are somewhat ambiguous

WHEN TO USE HIGH TEMPERATURE (0.6-1.0):
- User expresses frustration with previous attempts
- Complex, novel, or creative challenges
- Multiple failed attempts at same problem
- User explicitly asks for "different approach"
- Brainstorming or ideation tasks
- Breaking out of repetitive response patterns

ESCALATION SIGNALS (increase temperature):
- "That didn't work"
- "Try something different" 
- "Still having issues"
- "Not what I wanted"
- Repeated similar requests
- User frustration indicators

STABILITY SIGNALS (decrease temperature):
- "Perfect, do more like that"
- "Exactly what I needed"
- Clear, specific requirements
- Following established successful patterns

MODEL CHOICE PHILOSOPHY:
- anthropic/claude-sonnet-4: Best at creative coding. Does a bit more than asked. Use it most frequently.
- google/gemini-pro-2.5-preview: Use for single, simple tasks or for only planning. 
`,
                    messages: messageHandler.getMessagesForRequest({agentModeEnabled}).filter(m => !!m && m.role === "user"),
                    model: customModel("openai/gpt-4.1-mini"),
                    schema: z.object({
                        temperature: z.number()
                            .min(0.0)
                            .max(1.0)
                            .describe("Temperature value between 0.0-1.0 for the next response"),
                        model: z.string(z.enum([
                            'anthropic/claude-sonnet-4',
                            'google/gemini-pro-2.5-preview',
                        ])),

                        reasoning: z.string()
                            .describe("Brief explanation of why this temperature was chosen"),

                        contextFactors: z.array(z.enum([
                            "repeated_failures",
                            "user_frustration",
                            "creative_request",
                            "technical_precision_needed",
                            "clear_instructions",
                            "ambiguous_requirements",
                            "breakthrough_needed",
                            "pattern_following"
                        ])).describe("Key factors that influenced the temperature decision")
                    })
                })

                console.log('result', dynamicResult.object)

                const streamService = new StreamService({
                    // model,
                    messages: messageHandler.getMessagesForRequest({agentModeEnabled, applyCaching: dynamicResult.object.model.includes('claude')}).filter(m => !!m),
                    parser,
                    temperature: dynamicResult.object.temperature || 0.1,
                    modelId: dynamicResult.object.model,
                    diffParser,
                    dataStream,
                    userMessageId,
                    abortSignal: request.signal,
                    agentModeEnabled,
                    isConnectingSupabase,
                    isFirstMessage,
                    enableValidation: true,
                    isSupabaseConnected: isSupabaseConnected,
                    enabledTools,
                    tools: {
                        // getFileContents: getFileContents({
                        //     files,
                        //     fileManager,
                        //     dataStream,
                        //     creditUsageTracker,
                        //     initialPromptGuidelines,
                        //     isFirstMessage,
                        //     circuitBreakerState
                        // }),
                        addAiMemory: addAiMemory({
                            creditUsageTracker,
                            dataStream,
                            projectId
                        }),
                        getSupabaseInstructions: getSupabaseInstructions({
                            creditUsageTracker,
                            dataStream,
                            project
                        }),
                        querySupabaseContext: querySupabaseContext({
                            projectId,
                            messageId: userMessageId
                        }),
                        getSupabaseLogs: getSupabaseLogs({
                            dataStream,
                            creditUsageTracker,
                            project
                        }),
                        manageSupabaseAuth: manageSupabaseAuth({
                            dataStream,
                            creditUsageTracker,
                            project
                        }),
                        getClientLogs: getClientLogs({
                            dataStream,
                            creditUsageTracker,
                            logs: logs || [],
                            messageId: userMessageId
                        }),
                        getScreenshots: getScreenshots({
                            dataStream,
                            creditUsageTracker,
                            messageId: userMessageId,
                            projectId
                        }),
                        queryCodebase: queryCodebase({
                            projectId,
                            fileManager,
                            files,
                            messageId: userMessageId,
                            userMessage: (userMessage.content[0] as TextPart).text as string || userMessage.content as string || '',
                        }),
                        searchWeb: searchWeb({
                            dataStream,
                            creditUsageTracker
                        }),
                        editFile: editFileTool({
                            fileManager,
                            contentManager,
                            dataStream,
                            processImagePlaceholders,
                            processVideoPlaceholders,
                            creditUsageTracker,
                            projectId
                        }),

                        // clientTesting: clientTestingTool({
                        //     dataStream,
                        //     creditUsageTracker
                        // }),
                        // generateReactNativeScreens: generateReactNativeScreens({
                        //     dataStream,
                        //     fileManager,
                        //     parser,
                        //     userMessageId
                        // })
                    },
                    onFinish: async ({ response, usage, experimental_providerMetadata, finishReason, warnings, text, reasoning, logprobs, enableValidation }) => {
                        // Stop the overall request timer
                        performanceTracker.stopTimer(requestId, {
                            status: 'success',
                            messageId: userMessageId,
                            chatId: id
                        });

                        console.log('reasoning', reasoning)
                        // await exportData(response, 'response-messages');
                        // console.log(`[Message:${userMessageId}]: Got response`, response.id, finishReason, response.messages)

                        // Wait for all diff operations to complete before proceeding
                        console.log(`[Message:${userMessageId}]: Waiting for all diff operations to complete...`);
                        const diffsCompleted = await contentManager.waitForDiffsToComplete(30000); // Wait up to 30 seconds
                        if (!diffsCompleted) {
                            console.warn(`[Message:${userMessageId}]: Not all diff operations completed in time, proceeding anyway`);
                        } else {
                            console.log(`[Message:${userMessageId}]: All diff operations completed successfully`);
                        }
                        if ((["length", "stop", "content-filter", "other", "tool-calls"] as Array<FinishReason>).includes(finishReason)) {
                            console.log(`[Message:${userMessageId}]: Response is valid`)
                            if (userId) {
                                console.log(`[Message:${userMessageId}]: Session is valid`)
                                try {
                                    const responseMessagesWithoutIncompleteToolCalls =
                                        sanitizeResponseMessages(response.messages);
                                    console.log(`[Message:${userMessageId}]: Sanitized messages`)

                                    // console.log('responseMessagesWithoutIncompleteToolCalls', responseMessagesWithoutIncompleteToolCalls)
                                    // await exportData(responseMessagesWithoutIncompleteToolCalls, 'response-messages');

                                    const firstAssistantMessage: CoreAssistantMessage & {id: string} | undefined = responseMessagesWithoutIncompleteToolCalls.find(m => m.role === "assistant") as CoreAssistantMessage & {id: string} | undefined;

                                    dataStream.writeMessageAnnotation({
                                        messageIdFromServer: firstAssistantMessage?.id as any,
                                        messageVersion: 'v2'
                                    });
                                    const finalMessages = responseMessagesWithoutIncompleteToolCalls.map(
                                        (message, index) => {

                                            if (message.role === 'assistant') {


                                                const text: string = (((message.content?.[0] as TextPart)?.text) || message.content || '') as string;

                                                let sqlCount = 0;
                                                if(text && text.match) {
                                                    sqlCount = (text?.match(/<MO_DATABASE_QUERY[^>]*>[\s\S]*?<\/MO_DATABASE_QUERY>/g) || []).length;
                                                }

                                                creditUsageTracker.trackOperation('sql_query', sqlCount);
                                            }

                                            const typedMessage = (message as CoreAssistantMessage & {id: string});
                                            return {
                                                id: typedMessage.id,
                                                chatId: id,
                                                role: message.role,
                                                content: message.content,
                                                userId: userId,
                                                projectId: chat.projectId,
                                                remoteProviderId: response.id,
                                                remoteProvider: "open-router",
                                                componentContexts: [],
                                                autoFixed: !!isAutoFixed,
                                                hidden: !!isAutoFixed,
                                                finishReason: finishReason,
                                                parentUserMessageId: userMessageId || null,
                                                isAssistantGroupHead: typedMessage.id ===  firstAssistantMessage?.id,
                                                parentAssistantMessageId: typedMessage.id !== firstAssistantMessage?.id ?  firstAssistantMessage?.id: null,
                                                // Hack to ensure the messages are written in sequence
                                                createdAt: dayjs().add(index, "second").toDate(),
                                            };
                                        },
                                    );
                                    console.log(`[Message:${userMessageId}]: Generated final messages`)

                                    // Get all files and process placeholders
                                    const placeholderSubstitutedFiles = await Promise.all(fileManager.getFileItems().map(async fileItem => {
                                        fileItem.content = await processImagePlaceholders(fileItem.content);
                                        fileItem.content = await processVideoPlaceholders(fileItem.content);
                                        return fileItem;
                                    }));
                                    console.log(`[Message:${userMessageId}]: Replaced placeholders`);

                                    // Validate all files for syntax errors
                                    // console.log('Running validation on all files...');
                                    // const filesToValidate = placeholderSubstitutedFiles.map(file => ({
                                    //     name: file.name.split('/').pop() || 'file.ts',
                                    //     content: file.content
                                    // }));

                                    // const validationResult = TargetedValidator.validateFiles(filesToValidate);

                                    // If there are syntax errors, attempt to fix them with AI
                                    // if (!validationResult.isValid) {
                                    //     console.log('Found syntax errors in some files, attempting AI correction...');
                                    //     dataStream.writeData({
                                    //         type: 'status',
                                    //         content: 'Optimizing code structure...'
                                    //     });

                                    // Track this as an error fixing operation (exempt from credits)
                                    // creditUsageTracker.trackDiscountedOperation('file_change', 'error_fixing', 1);

                                    // Send notification to client about free error fixing
                                    // dataStream.writeData({
                                    //     type: 'credit-usage-event',
                                    //     content: {
                                    //         type: 'error_fixing',
                                    //         message: 'Error fixing doesn\'t consume credits! Keep your app running smoothly.'
                                    //     }
                                    // });

                                    // Collect files with errors
                                    // await runLintAndFixErrors(validationResult, placeholderSubstitutedFiles, fileManager, dataStream, creditUsageTracker);

                                    // if (enableValidation && isFirstMessage) {
                                    //     dataStream.writeData({
                                    //         type: 'validating-output',
                                    //         content: {
                                    //             type: 'validating_output',
                                    //             message: 'We are checking the generated code for errors and logical correctness. Any errors will be fixed automatically.'
                                    //         }
                                    //     });
                                    //     const validationStreamService = streamService.withConfig({
                                    //         enableValidation: false,
                                    //         messages: [...responseMessagesWithoutIncompleteToolCalls, {
                                    //             role: 'user',
                                    //             content: `Look at your previous response and the files you have created/modified and find any errors that may cause the app to fail.
                                    //             Fix only the bugs and errors that you are 100% sure, otherwise do not say anything.
                                    //             Do not fix the errors that will not cause any issues. Look for missing imports, minified react errors like hooks after a condition or other potential minified react errors.
                                    //             Also, fix any design inconsistencies if found. Remember, writing code at this stage is costly and hence its better not to write code unless absolutely essential.
                                    //             Just respond with Validate Code, nothing else.
                                    //
                                    //             If the file is as tsx file and the replacement needs to be on the JSX or the styles, use MO_FILE.
                                    //             If the file is a ts file and you can uniquely identify the elements to replace, use MO_DIFF.
                                    //             NEVER use MO_DIFF for JSX replacements or complex structures or the styles.
                                    //             TRY to use MO_DIFF with as unique a chunk of code you can, for example, whole functions, whole styles or whole data
                                    //
                                    //             ONLY fix errors your are 100% sure that will lead to an error. Don't fix anything else. It costs us a lot of money.
                                    //             Specifically look for these errors. You will not find these errors, you will have use your intuition. Use the MO_FILE to fix them and overwrite the entire file without changing the design or functionality of the file:
                                    //
                                    //              'is not defined' -> A variable is used but not defined
                                    //              'Unexpected token, expected "," ', -> A "'" may be used withing single quotes, either escape it or use double quotes
                                    //              'Cannot read properties of undefined' -> A property is being accessed from somewhere, most probably a store but is not being handled for undefined values. Second issue could be not using both the theme in the Navigation as defined in the system prompt
                                    //              'Minified React error' -> Can be hooks, maximum call render execeeded, or any related codebase
                                    //              'Usage of typeof variable[0]' -> Instead use any or create a proper interface and use typeof interface
                                    //              'Usage of union types' -> Union types are not supported in the current environment
                                    //              Check for navigation connections -> HomeScreen should connect to the appropriate screens and all screens should be connected to the home screen with a clear navigation path.
                                    //              No DEAD ends -> Ensure there are no screens with dead ends
                                    //              FUNCTIONALITY REDIRECTION -> Make sure to check for redirection after a specific action.
                                    //              DEPENDENCY CHECKS => Check for dependencies added that are not in the approved list
                                    //              - For example, after login, user should be redirected to the appropriate screen.
                                    //              - Logout, should logout the user and redirect to the login screen.
                                    //              - Saving/Adding/Deleting data should navigate to the previous screen
                                    //
                                    //             `
                                    //         }]
                                    //     });
                                    //
                                    //     dataStream.writeData({
                                    //         type: 'validated-output',
                                    //         content: {
                                    //             type: 'validated_output',
                                    //             message: 'Validation of your project is compelete.'
                                    //         }
                                    //     });
                                    //     const result2 = await validationStreamService.startStream();
                                    //     result2.mergeIntoDataStream(dataStream, {sendUsage: false, sendReasoning: true});
                                    // }

                                    // }



                                    // Process Supabase functions and secrets if project is linked to Supabase
                                    let supabaseArtifactHandler: SupabaseArtifactHandler | null = null;
                                    let supabaseConnectionId: string | null = null;
                                    let supabaseProjectRef: string | null = null;

                                    supabaseArtifactHandler = new SupabaseArtifactHandler(supabaseIntegrationProvider);

                                    // Check if chat is linked to a Supabase project
                                    if (project?.supabaseProjectId && project?.connectionId) {
                                        supabaseConnectionId = project.connectionId;
                                        supabaseProjectRef = project.supabaseProjectId;
                                    }

                                    console.log('Dirty files', fileManager.getDirtyFiles().map(file => file.path))

                                    // Set needsContinuation to true if agent mode is enabled and finish reason is length or tool-calls
                                    const needsContinuation = agentModeEnabled && (finishReason === 'length' || finishReason === 'tool-calls');
                                    console.log(`[Message:${userMessageId}]: Setting needsContinuation to ${needsContinuation} (agentMode: ${agentModeEnabled}, finishReason: ${finishReason})`);

                                    dataStream.writeData({
                                        type: 'continuation-flag',
                                        content: {
                                            needsContinuation
                                        }
                                    })
                                    // console.log('response.modelId', response.modelId)
                                    // console.log('supabaseProjectRef', supabaseProjectRef)
                                    // console.log('supabaseConnectionId', supabaseConnectionId)
                                    // Save both messages and final file state
                                    const [messages] = await Promise.all([
                                        saveMessages({
                                            messages: finalMessages
                                        }),
                                        saveFileStateAndCacheIfNeeded({
                                            chatId: id,
                                            messageId: userMessageId, // Use user message ID since file state reversal is tied to user messages
                                            files: placeholderSubstitutedFiles,
                                            shouldCache: shouldCreateNewBase, // Mark as base if we need a new base version
                                            dependencies: dependencies || DEFAULT_DEPENDENCIES
                                        }),
                                        saveTokenConsumption({
                                            chatId: id,
                                            messageId: userMessageId,
                                            model: response.modelId,
                                            completionTokens: parseInt(usage.completionTokens as any || "0"),
                                            totalTokens: parseInt(usage.totalTokens as any || "0"),
                                            promptTokens:parseInt( usage.promptTokens as any || "0"),
                                            totalTimeToken: (Date.now() - startTime)/1000,
                                            userId: userId,
                                            projectId: projectId,
                                            isAnonymous: isAnonymous,
                                            remoteProviderId: response.id,
                                            remoteProvider: "open-router",
                                            creditsConsumed: creditUsageTracker.getCreditConsumption() || 0,
                                            discountReason: creditUsageTracker.getDiscountedCreditCount() ? 'error_fixing': undefined,
                                            discountedCredits: creditUsageTracker.getDiscountedCreditCount() || 0,
                                            errorId: errorUuid,
                                            isAutoFixed: isAutoFixed
                                        }),
                                        updateCreditUsage(userId, creditUsageTracker.getOperations()),
                                        enhancedRequirements ? updateProject({
                                            id: projectId,
                                            initialUXGuidelines: enhancedRequirements,
                                        }) : null,
                                        // Update the chat with the new needsContinuation flag
                                        await updateChat({
                                            id,
                                            updatedAt: new Date(),
                                            needsContinuation
                                        }),
                                        // Process Supabase functions and secrets if available
                                        (supabaseArtifactHandler && supabaseConnectionId && supabaseProjectRef) ?
                                            supabaseArtifactHandler.processFunctions(
                                                placeholderSubstitutedFiles,
                                                supabaseConnectionId,
                                                supabaseProjectRef,
                                                process.cwd(),
                                                dataStream,
                                                // Pass dirty file paths to only deploy modified functions
                                                fileManager.getDirtyFiles().map(file => file.path)
                                            ) : null,
                                        (supabaseArtifactHandler) ?
                                            supabaseArtifactHandler.processSecrets(
                                                placeholderSubstitutedFiles,
                                                dataStream
                                            ) : null,
                                    ])

                                    console.log(streamService.getToolCallCostReport());
                                    // if(creditUsageTracker.getDiscountedCreditCount() !== 0) {
                                    //     dataStream.writeData({
                                    //         type: 'credit-usage-event',
                                    //         content: {
                                    //             eventType: 'error-fix-free',
                                    //             messageId: userMessageId
                                    //         }
                                    //     });
                                    // }


                                    dataStream.writeData({
                                        type: 'messages-update',
                                        content: {
                                            messages: [{id: userMessageId, role: userMessage.role}, ...((messages as DBMessage[]).map(m => {
                                                return {
                                                    id: m.id,
                                                    role: m.role,
                                                    parentUserMessageId: m.parentUserMessageId,
                                                    parentAssistantMessageId: m.parentAssistantMessageId,
                                                    isAssistantGroupHead: m.isAssistantGroupHead
                                                }
                                            }))]
                                        }
                                    })
                                    dataStream.writeData({
                                        type: 'ai-message-id',
                                        content: (messages[0] as Message).id,
                                    });
                                    console.log(`[Message:${userMessageId}]: Saved messages and file State`)
                                } catch (error) {
                                    console.log(`[Message:${userMessageId}]: Failed to save chat`)
                                    console.error('Failed to save chat');
                                    console.log('response', error, JSON.stringify(response, null, 2))
                                }
                            }
                        } else {
                            if(startedStreaming) {
                                // We need to continue the stream no matter what.
                            }
                            console.log(`[Message:${userMessageId}]: Finish reason is not valid - Reason:${finishReason}`)
                            console.log('finishReason in not tracked', finishReason, JSON.stringify(response, null, 2));
                        }
                    },
                });


                // console.log('messageHandler.getMessagesForRequest()', messageHandler.getMessagesForRequest().map(a => JSON.stringify(a)).join(','))
                const startTime = Date.now();
                const result = await streamService.startStream();

                // No need to reset the tool tracker since it's request-scoped
                // and will be garbage collected when the request ends
                ToolCountTracker.getInstance().resetCounts(userMessageId)

                result.mergeIntoDataStream(dataStream, {sendUsage: false, sendReasoning: true});

                // Stop the AI response timer
                performanceTracker.stopTimer(aiResponseTimerId, {
                    status: 'success',
                    messageId: userMessageId,
                    chatId: id
                });

                // Stop the overall request timer
                performanceTracker.stopTimer(requestId, {
                    status: 'success',
                    messageId: userMessageId,
                    chatId: id
                });
            } catch (e: any) {
                console.log('Error', e)

                // Stop timers with error status
                performanceTracker.stopTimer(aiResponseTimerId, { status: 'error', error: e.message });
                performanceTracker.stopTimer(requestId, { status: 'error', error: e.message });

                throw e;
            }
        },
    });
}

export async function DELETE(request: Request) {
    const requestId = generateUUID();
    const performanceTracker = new PerformanceTracker();
    performanceTracker.startTimer(requestId, 'DELETE /api/chat', { endpoint: 'DELETE /api/chat' });

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
        performanceTracker.stopTimer(requestId, { status: 'error', error: 'Chat ID not provided' });
        return new Response('Not Found', { status: 404 });
    }

    const authTimerId = `${requestId}-auth`;
    performanceTracker.startTimer(authTimerId, 'Authentication', { requestId }, requestId);
    const session = await auth();
    performanceTracker.stopTimer(authTimerId);

    if (!session || !session.user) {
        performanceTracker.stopTimer(requestId, { status: 'error', error: 'Unauthorized' });
        return new Response('Unauthorized', { status: 401 });
    }

    try {
        const chatLookupTimerId = `${requestId}-chat-lookup`;
        performanceTracker.startTimer(chatLookupTimerId, 'Chat Lookup', { requestId, chatId: id }, requestId);
        const chat = await getChatById({ id });
        performanceTracker.stopTimer(chatLookupTimerId);

        if (chat.userId !== session.user.id) {
            performanceTracker.stopTimer(requestId, { status: 'error', error: 'Unauthorized' });
            return new Response('Unauthorized', { status: 401 });
        }

        const deleteTimerId = `${requestId}-delete-chat`;
        performanceTracker.startTimer(deleteTimerId, 'Delete Chat', { requestId, chatId: id }, requestId);
        await deleteChatById({ id });
        performanceTracker.stopTimer(deleteTimerId);

        performanceTracker.stopTimer(requestId, { status: 'success', chatId: id });
        return new Response('Chat deleted', { status: 200 });
    } catch (error: any) {
        performanceTracker.stopTimer(requestId, { status: 'error', error: error.message });
        return new Response('An error occurred while processing your request', {
            status: 500,
        });
    }
}

'use client';

import type {Attachment, Message, CreateMessage} from 'ai';
import {useChat} from '@ai-sdk/react';
import {marked} from 'marked';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import useSWR, {useSWRConfig} from 'swr';
import type {Vote} from '@/lib/db/schema';
import {fetcher, generateUUID, convertToUIMessages} from '@/lib/utils';
import {MultimodalInput} from './multimodal-input';
import {Messages} from './messages';
import {useBlockSelector} from '@/hooks/use-block';
import {ChatHeader} from "@/components/base/chat-header";
import {FileItem} from "@/types/file";
import {Block} from "@/components/base/block";
import {observer} from 'mobx-react-lite';
import {useStores} from "@/stores/utils/useStores";
import {toast} from "sonner";
import {UpgradeDialog} from '@/components/upgrade-dialog';
import {useAnonymousSession} from "@/providers/anonymous-provider";
import {useAgentModeSettings} from "@/components/settings/agent-mode-settings";
import {DEFAULT_CODE} from "@/types/editor";
import {trackMessageEvent} from "@/lib/analytics/track";
import {ActionMeta} from "@/lib/parser/ActionsParser";
import {IntegrationDialog} from "@/components/generator/IntegrationDialog";
import {SecretInput} from "@/components/supabase/SecretInput";
import {useSession} from "next-auth/react";
import * as Sentry from "@sentry/nextjs";
import {JSONValue, UIMessage} from "@ai-sdk/ui-utils";
import {SQLExecutionDialog} from "@/components/base/sql-execution-dialog";
import {CodeChangeAnalysis} from "@/lib/services/post-stream-analysis-service";
import {ConfirmationDialog} from "@/components/ui/confirmation-dialog";
import {AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle} from "@/components/ui/alert-dialog";
import {Button} from "@/components/ui/button";
import {AlertCircle, CheckCircle, AlertTriangle} from "lucide-react";
import type {SQLStatus} from "@/hooks/use-sql-status";
import {delay} from "lodash";
import {useAutoResume} from "@/hooks/use-auto-resume";

const Chat = observer(({
                           id,
                           initialMessages,
                           isReadonly,
                           initialPrompt,
                           onLoadingChange,
                           projectId,
                           runLastUserMessage,
                           onInitialRunInitialized,
                           hasMoreMessages,
                           totalUserMessages
                       }: {
    id: string;
    initialMessages: Array<UIMessage>;
    initialPrompt?: string;
    isReadonly: boolean;
    onLoadingChange?: (isLoading: boolean) => any;
    projectId: string;
    runLastUserMessage: boolean;
    onInitialRunInitialized: () => any;
    hasMoreMessages?: boolean;
    totalUserMessages?: number;
}) => {
    const {mutate} = useSWRConfig();

    const {anonymousId, hasReachedLimit, incrementChatCount, storeChat, getStoredChat} = useAnonymousSession();
    const {status: authStatus} = useSession();

    const {generatorStore, integrationStore, logStore, snackStore} = useStores();
    const session = generatorStore.getActiveSession(id);
    const [integrationOpen, setIntegrationOpen] = useState(false);
    const [isSecretOpen, setIsSecretOpen] = useState(false);
    const [secretNames, setSecretNames] = useState<string[] | null>(null);
    const [droppedFiles, setDroppedFiles]= useState<File[]>();
    const [selectMode, setSelectMode] = useState(false);
    const [sqlDialogOpen, setSqlDialogOpen] = useState(false);
    // Define a proper type for SQL queries
    type SqlQuery = {
        query: string;
        source: string;
        table: string;
    };
    const [pendingSqlQueries, setPendingSqlQueries] = useState<SqlQuery[]>([]);
    const [analysisResult, setAnalysisResult] = useState<{ analysis: CodeChangeAnalysis, message: string } | null>(null);
    const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false);
    const [isLoadingPrevious, setIsLoadingPrevious] = useState(false);
    const [isLoadAllDialogOpen, setIsLoadAllDialogOpen] = useState(false);
    const [hasMoreLocalOverride, setHasMoreLocalOverride] = useState<boolean | undefined>(undefined);
    if (!session) {
        return;
    }

    const {agentModeEnabled} = useAgentModeSettings();

    // Track when a chat is started or resumed
    useEffect(() => {
        const isNewChat = initialMessages.length <= 1; // Only system message or empty

        if (isNewChat) {
            trackMessageEvent('CHAT_STARTED', {
                chat_id: id,
                project_id: projectId
            });
        } else {
            trackMessageEvent('CHAT_RESUMED', {
                chat_id: id,
                time_since_last_activity: 0, // We don't have this info yet, but could add it
                session_message_count: initialMessages.length
            });
        }
    }, [id, initialMessages.length, projectId]);

    const {
        messages,
        setMessages,
        handleSubmit,
        input,
        setInput,
        append,
        isLoading,
        stop,
        reload,
        data,
        status,
        experimental_resume,
        addToolResult
    } = useChat({
        id,
        keepLastMessageOnError: false,
        experimental_prepareRequestBody: (options: {
            id: string;
            messages: UIMessage[];
            requestData?: JSONValue;
            requestBody?: object;
        }) => {
            options.messages = options.messages.map(message => {
                message.parts = message.parts.filter(part => {
                    if(part.type === "tool-invocation") {
                        return part.toolInvocation.state !== "partial-call"
                    }
                    return true
                })
                return message
            }).slice(-20);
            return {
                files: session.fileTree.length ? session.fileTree : DEFAULT_CODE,
                activeFile: session.activeFile,
                dependencies: session.dependencies,
                linkSupabaseProjectId: integrationStore.currentSelectedProjectId,
                linkSupabaseConnection: integrationStore.getConnection("supabase")?.id,
                projectId: session.projectId,
                logs: logStore.getLogs(id),
                agentModeEnabled: agentModeEnabled,
                messages: options.messages,
                id,
                ...options.requestBody
            }
        },
        headers: anonymousId ? {
            'x-anonymous-id': anonymousId,
            'x-chat-count': hasReachedLimit ? '2' : '1',
        } : {},
        initialMessages,
        generateId: () => {
            return generateUUID();
        },
        sendExtraMessageFields: true,
        streamProtocol: "data",
        onToolCall: async ({toolCall}) => {
            if(toolCall.toolName === "clientTesting") {
                console.log('[Chat] Client testing tool call received:', toolCall);
                // if (session) {
                //     // Start client testing with the tool call parameters
                //     session.startClientTesting(
                //         toolCall.toolCallId,
                //         (toolCall.args as any)?.featuresToTest || '',
                //         (toolCall.args as any)?.expectations || '',
                //         (toolCall.args as any)?.reason || ''
                //     );
                //
                //     console.log('Waiting')
                //     // We don't return a result immediately - the UI component will handle this
                //     // The result will be added when the user clicks "I am done testing"
                //     await new Promise((resolve) => {
                //         // This promise is never resolved directly
                //         // Instead, we use addToolResult when the user completes testing
                //         setTimeout(() => {
                //             resolve("DONE");
                //         }, 30000);
                //     });
                // }
                //
                // console.log('Now completing')
                // // Fallback if no session is available
                // return {
                //     result: "DONE",
                //     toolCallId: toolCall.toolCallId
                // };
            }
        },
        onResponse: () => {
            session.setNeedsContinuation(false);
            setSelectMode(false);
            // Track when AI begins streaming a response
            trackMessageEvent('AI_RESPONSE_STREAMING', {
                chat_id: id
            });
        },
        onFinish: async (message) => {
            // Track when a message is finished
            // Use a valid event type from the available options
            trackMessageEvent('RECEIVED', {
                chat_id: id,
                message_id: message.id
                // Remove properties that don't exist in MessageEventProperties
            });
            
            // Check if the message contains SQL queries
            if (typeof message.content === 'string' && message.content.includes('<MO_DATABASE_QUERY')) {
                // Extract SQL queries using regex
                const regex = /<MO_DATABASE_QUERY[^>]*source=["']([^"']*)["'][^>]*(?:table=["']([^"']*)["'])?[^>]*>([\s\S]*?)<\/MO_DATABASE_QUERY>/g;
                const queries: SqlQuery[] = [];
                let match;
                
                while ((match = regex.exec(message.content)) !== null) {
                    const source = match[1];
                    const table = match[2] || '';
                    const query = match[3].trim();
                    
                    if (query) {
                        // Add typed query to the array
                        const sqlQuery: SqlQuery = {
                            query,
                            source,
                            table
                        };
                        queries.push(sqlQuery);
                    }
                }
                
                if (queries.length > 0) {
                    setPendingSqlQueries(queries);
                    setSqlDialogOpen(true);
                }
            }
            
            session.markStreamingComplete();
            console.log('Generated message ID', message.id)
            // Track when AI completes a response
            trackMessageEvent('AI_RESPONSE_RECEIVED', {
                chat_id: id,
                message_content_length: message.content.length
            });

            // try {
            //     delay(() => {
            //         session.performAnalysis(message, messages);
            //     }, 500);
            // } catch (e) {
            //     console.log('Error performing analysis', e)
            // }

            if (anonymousId) {
                // Store messages and file state for anonymous users
                storeChat(id, [...initialMessages, message], session.fileTree);
                if (!hasReachedLimit) {
                    incrementChatCount();
                }
            } else {
                // Update both history and subscription status for authenticated users
                mutate('/api/history');
                mutate('/api/subscription/status');
            }
        },
        onError: (error) => {
            session.markAsFailed();
            Sentry.withScope(function (scope) {
                scope.setTag("action", "chat");
                scope.setLevel("error");
                Sentry.captureException(error);
            });

            // Track message error
            trackMessageEvent('ERROR', {
                chat_id: id,
                error_type: error.message?.includes("limit reached") ? 'rate_limit' : 'server_error',
                error_message: error.message || 'Unknown error'
            });

            try {
                let draft = '';
                // const lastUserMessageIndex = messages.findLastIndex(message => message.role === "user");
                // setMessages(messages => {
                //     draft = messages[lastUserMessageIndex].content;
                //     messages = messages.slice(0, lastUserMessageIndex);
                //     return messages;
                // });
                toast.error("There was an error with our LLM provider while processing your last message. You will not be charged for it.");
                setInput(draft)
                const errorData = JSON.parse(error.message);
                // Regardless of the limit reached, we want to first show the upgrade dialog to anonymous users and upgrade dialog once they are logged in
                if (errorData.error?.includes("limit reached")) {
                    if (errorData.isAnonymous) {
                        generatorStore.toggleLoginDialog(true);
                    } else {
                        generatorStore.setUsageLimit(
                            errorData.limit,
                            errorData.remaining
                        );
                        generatorStore.toggleUpgradeDialog(true);
                    }
                } else {
                    toast.error('An error occurred while processing your message');
                }
            } catch (e) {
                console.error('Failed to parse error:', error);
                toast.error('An error occurred while processing your message');
            }
        }
    });

    const {data: votes} = useSWR<Array<Vote>>(
        `/api/vote?chatId=${id}`,
        fetcher,
    );

    const [attachments, setAttachments] = useState<Array<Attachment>>([]);

    const isBlockVisible = useBlockSelector((state) => state.isVisible);
    const [initialPromptAdded, setInitialPromptAdded] = useState(false);
    const [lastRunInitialized, setLastRunInitialized] = useState(false);

    const appendInitialPrompt = useCallback(() => {
        if (initialPrompt && append && !initialPromptAdded) {
            console.log('Append');
            setInitialPromptAdded(true)
            append({
                id: id,
                role: "user",
                content: initialPrompt
            }).then().catch(err => {
                console.log('Error appending initial prompt added for user', err);
                setInput(initialPrompt);
                handleSubmit();
            });
            console.log('Can fire now');
            // window.history.replaceState({}, '', `/generator`);
        }
    }, [initialPrompt, id, append]);

    useEffect(() => {
        appendInitialPrompt();
    }, [appendInitialPrompt]);

    useEffect(() => {
        if(session.updatedUserMessage) {
            // Let's change the latest user message and clear the updatedUserMessage prop
            setMessages(messages => {
                const updateMessages = [...messages];
                const lastUserMessageIndex = updateMessages.findLastIndex(message => message.role === "user");
                if(lastUserMessageIndex !== null) {
                    updateMessages[lastUserMessageIndex].content = session.updatedUserMessage;
                }
                return updateMessages;
            })
            session.onUserMessageUpdate('');
        }
    }, [session.updatedUserMessage])

    // useEffect(() => {
    //     if(session?.needsContinuation && ["ready"].includes(status)) {
    //         append({
    //             content: "Please continue. First start by printing the plan of what is done and what is left with clear indicators. Make sure to be on track and don't drift doing things not asked to do. Batch the edits in a single call. Please be careful and make changes that are holistic and coherent. Pay close attention to the logs and use the correct tools judiciously.",
    //             role: "user"
    //         })
    //     }
    // }, [session?.needsContinuation])


    const hasRunRef = useRef(false);

    const initiateLatestMessageAutoRun = useCallback(() => {
        if (runLastUserMessage && !lastRunInitialized && !hasRunRef.current) {
            onInitialRunInitialized();
            hasRunRef.current = true;
            setLastRunInitialized(true);
            reload({
                body: {
                    isReload: true,
                    isInitial: true
                }
            })
        }
    }, [runLastUserMessage, reload, lastRunInitialized, setLastRunInitialized])

    const onVisualSelectionClicked = () => {
        snackStore.sendMessageToSnack(id, 'TOGGLE_INSPECTION_MODE');
        setSelectMode(selectMode => !selectMode);
    }


    useEffect(() => {
        initiateLatestMessageAutoRun()
    }, [initiateLatestMessageAutoRun])


    useEffect(() => {
        if (integrationStore.currentSelectedProjectId && integrationStore.shouldSendMessage) {
            append({
                content: `Please connect my supabase project ${integrationStore.currentSelectedProjectId} with this app`,
                role: "user"
            })
                .then(() => {
                    toast.success('Your app is now linked to supabase');
                })
                .catch(err => {
                    toast.success('Failed to link app to supabase project');
                })
                .finally(() => {
                    integrationStore.setShouldSendMessage(false);
                    integrationStore.resetCurrentSelectedProjectId();
                })
        }
    }, [integrationStore.currentSelectedProjectId, integrationStore.shouldSendMessage]);

    useEffect(() => {
        // Update session status based on loading state
        if (isLoading) {
            window.history.replaceState({}, '', `/projects/${session.projectId}/chats/${id}`);
            session.setStatus('streaming');
        } else {
            session.setStatus('idle');
        }

        // Notify parent component if needed
        if (onLoadingChange) {
            onLoadingChange(isLoading);
        }
    }, [isLoading]);

    // Watch for error fix requests
    useEffect(() => {
        if (session.currentMessage) {
            setInput(session.currentMessage);
            session.setCurrentMessage(''); // Clear after sending
        }
    }, [session.currentMessage]);
    
    // Create local state to track component contexts
    const [localComponentContexts, setLocalComponentContexts] = useState<any[]>([]);
    
    // Watch for component context changes
    useEffect(() => {
        // Make a shallow copy of the observable array to trigger React's change detection
        setLocalComponentContexts([...session.componentContexts]);
    }, [session.componentContexts, session.componentContexts.length]);

    useEffect(() => {
        if(session.currentFile) {
            setDroppedFiles([session.currentFile]);
        }
    }, [session.currentFile])

    const selectionChange = (model: string) => {
        // setModelId(model);
    }

    const onVersionClick = (messageId: string) => {
        const messageIndex = messages.findIndex((message) => message.id === messageId);
        // Find the next previous user message before the current message
        const previousUserMessage = messages.find((message, index) => message.role === 'user' && index < messageIndex);

        if (previousUserMessage) {
            session.loadHistoricalVersion(projectId, id, previousUserMessage.id, messageId);
        } else {
            toast.warning('No previous state message found');
        }
    }

    const onActionClick = (action: ActionMeta) => {
        console.log('action.type', action.type, action.type.includes("supabase_integration"))
        const {type, content, tool, link, secretName, allSecretNames} = action;
        if (status !== 'ready' && status !== 'error') {
            toast.warning("Please wait for the previous response to be completed before using this action.")
            return;
        }
        // Handle button click based on type
        if (type === 'tool' && tool) {
            console.log(`Tool action: ${tool}`);
            // Here you would trigger the tool
            switch (tool) {
                case 'supabase_integration':
                    if (authStatus !== "authenticated") {
                        generatorStore.toggleLoginDialog(true);
                        return;
                    }
                    setIntegrationOpen(true);
                    break;

                case 'secrets_form':
                    if (allSecretNames?.length) {
                        if (authStatus !== "authenticated") {
                            generatorStore.toggleLoginDialog(true);
                            return;
                        }
                        setSecretNames(allSecretNames);
                        setIsSecretOpen(true);
                    } else {
                        append({
                            content: `The secret names are not set in the action. Please give an actions with the correct secretName for me to enter.`,
                            role: 'user'
                        })
                    }
                    break;
            }
        } else if (type === "feature" || type === "code") {
            append({
                content: content,
                role: 'user'
            })
        } else {
            console.log(`Action clicked: ${type} - ${content}`);
        }
    }

    const onSecretSubmit = async (secretValues: Record<string, string>): Promise<void> => {
        const result = await session.saveSecrets(secretValues, projectId, append as any);
        if (result) {
            setIsSecretOpen(false);
        }
        setSecretNames(null);
    }
    
    // Function to load all messages in the background
    const loadAllMessages = async () => {
        if (isLoadingPrevious || !hasMoreMessages) return;
        
        try {
            // Close the dialog immediately
            setIsLoadAllDialogOpen(false);
            
            // Set loading state
            setIsLoadingPrevious(true);
            toast.info('Loading all messages in the background...');
            
            // Fetch all messages for this chat
            const response = await fetch(`/api/chats/${id}/messages?all=true`);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch all messages: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            // Convert DB messages to UI messages
            const allMessages = convertToUIMessages(data.messages);
            
            // Replace current messages with all messages
            if (allMessages.length > 0) {
                setMessages(allMessages);
                toast.success(`Loaded all ${allMessages.length} messages`);
                
                // Hide the load more button by setting hasMoreMessages to false
                if (totalUserMessages && allMessages.length >= totalUserMessages) {
                    // If we've loaded all messages, update the state to hide the button
                    if (hasMoreMessages) {
                        // Only update if needed to avoid unnecessary re-renders
                        // This is a local state update since we can't modify the prop directly
                        setHasMoreLocalOverride(false);
                    }
                }
            } else {
                toast.info('No messages found');
            }
        } catch (error) {
            console.error('Error loading all messages:', error);
            toast.error('Failed to load all messages');
        } finally {
            setIsLoadingPrevious(false);
        }
    };
    
    // Function to show confirmation dialog
    const showLoadAllConfirmation = useCallback(() => {
        setIsLoadAllDialogOpen(true);
    }, [setIsLoadAllDialogOpen]);

    const integrationClicked = (open: boolean) => {
        setIntegrationOpen(open)
    }

    const setSnackError = (error: any) => {
        session.setSnackError(error, 'supabase');
    }

    // Function to execute SQL queries
    const executeSqlQuery = async (query: SQLStatus): Promise<string | null> => {
        try {
            const response = await fetch(`/api/project/${projectId}/sql`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query.query }),
            });

            const data = await response.json();
            if (!response.ok) {
                return data.error || data.message || 'Failed to execute query';
            }
            
            return null; // No error
        } catch (err: any) {
            return err.message || 'An unexpected error occurred';
        }
    };
    
    // Handle dialog close
    const handleSqlDialogComplete = () => {
        setSqlDialogOpen(false);
        setPendingSqlQueries([]);
    };

    // useAutoResume({
    //     autoResume: true,
    //     initialMessages,
    //     experimental_resume,
    //     data,
    //     setMessages,
    // });
    
    // Wrap reload and append from useChat in stable references
    const stableReload = useCallback((opts?: any) => reload(opts), [reload]);
    const stableAppend = useCallback((msg: Message | CreateMessage, opts?: any) => append(msg, opts), [append]);

    // Stable wrapper for version and action clicks
    const stableOnVersionClick = useCallback((messageId: string) => onVersionClick(messageId), [onVersionClick]);
    const stableOnActionClick = useCallback((action: ActionMeta) => onActionClick(action), [onActionClick]);

    // Stable snack error setter
    const stableSetSnackError = useCallback((error: any) => setSnackError(error), [setSnackError]);

    return (
        <>
            <div className="flex flex-col h-full bg-background overflow-hidden">
                {/* Main scrollable area */}
                <div className="flex-1 overflow-y-auto pb-safe relative">
                    <div className="flex flex-col h-full min-h-0">
                        <Messages
                            votes={votes}
                            chatId={id}
                            projectId={projectId}
                            isLoading={isLoading}
                            messages={messages}
                            setMessages={setMessages}
                            reload={stableReload}
                            append={stableAppend}
                            isReadonly={isReadonly}
                            isBlockVisible={isBlockVisible}
                            setInput={setInput}
                            setAttachments={setAttachments}
                            onVersionClick={stableOnVersionClick}
                            lastActiveVersionId={''}
                            onActionClick={stableOnActionClick}
                            setSnackError={stableSetSnackError}
                            hasMoreMessages={hasMoreLocalOverride !== undefined ? hasMoreLocalOverride : hasMoreMessages}
                            totalUserMessages={totalUserMessages}
                            onLoadPrevious={showLoadAllConfirmation}
                            isLoadingPrevious={isLoadingPrevious}
                            removeActions={false}
                            status={status}
                            addToolResult={addToolResult}
                        />
                    </div>
                </div>

                {/* Input area - with safe area padding */}
                {!isReadonly && (
                    <div
                        className="flex-shrink-0 border-t border-border/50 bg-background backdrop-blur bg-opacity-45 pb-safe pt-2">
                        <div className="px-4 py-2 mx-auto">
                            <MultimodalInput
                                chatId={id}
                                input={input}
                                setInput={setInput}
                                handleSubmit={handleSubmit}
                                isLoading={isLoading}
                                stop={stop}
                                inDesignMode={false}
                                attachments={attachments}
                                setAttachments={setAttachments}
                                messages={messages}
                                setMessages={setMessages}
                                append={append}
                                projectId={projectId}
                                needsContinuation={session.needsContinuation}
                                droppedFiles={droppedFiles}
                                onVisualSelectionClicked={onVisualSelectionClicked}
                                componentContexts={localComponentContexts}
                                onRemoveComponentContext={(id) => session.removeComponentContext(id)}
                                onClearComponentContexts={() => session.clearComponentContexts()}
                                selectMode={selectMode}
                            />
                        </div>
                    </div>
                )}
            </div>

            {/*<Block*/}
            {/*    chatId={id}*/}
            {/*    input={input}*/}
            {/*    setInput={setInput}*/}
            {/*    handleSubmit={handleSubmit}*/}
            {/*    isLoading={isLoading}*/}
            {/*    stop={stop}*/}
            {/*    attachments={attachments}*/}
            {/*    setAttachments={setAttachments}*/}
            {/*    append={append}*/}
            {/*    messages={messages}*/}
            {/*    setMessages={setMessages}*/}
            {/*    reload={reload}*/}
            {/*    votes={votes}*/}
            {/*    isReadonly={isReadonly}*/}
            {/*/>*/}

            <IntegrationDialog
                providerId={'supabase'}
                open={integrationOpen}
                chatId={id}
                onOpenChange={integrationClicked}
            />

            <SecretInput
                isOpen={isSecretOpen && !!secretNames}
                onClose={() => setIsSecretOpen(false)}
                onSubmit={onSecretSubmit}
                secretNames={secretNames as string[]}
            />
            
            {/* SQL Execution Dialog */}
            <SQLExecutionDialog
                queries={pendingSqlQueries.map((q: SqlQuery) => ({
                    type: 'up',
                    source: q.source,
                    table: q.table || 'database',
                    description: `Setting up ${q.table || 'database'} for ${q.source}`,
                    state: 'pending',
                    query: q.query
                }))}
                messageId=""
                isOpen={sqlDialogOpen}
                onExecute={executeSqlQuery}
                onComplete={handleSqlDialogComplete}
                setInput={setInput}
            />
            
            {/* Code Analysis Dialog */}
            {/* Confirmation dialog for loading all messages */}
            <ConfirmationDialog
                title="Load All Messages"
                description="Loading all messages may impact performance, especially for large conversations. Are you sure you want to load all messages?"
                confirmText="Load All Messages"
                cancelText="Cancel"
                open={isLoadAllDialogOpen}
                onOpenChange={setIsLoadAllDialogOpen}
                onConfirm={loadAllMessages}
                hideTriggerButton
            />
            
            <AlertDialog open={isAnalysisDialogOpen} onOpenChange={setIsAnalysisDialogOpen}>
                <AlertDialogContent className="max-w-3xl">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            {analysisResult?.analysis.recommendedAction === 'continue' && (
                                <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                            {analysisResult?.analysis.recommendedAction === 'fix' && (
                                <AlertTriangle className="h-5 w-5 text-amber-500" />
                            )}
                            {analysisResult?.analysis.recommendedAction === 'redo' && (
                                <AlertCircle className="h-5 w-5 text-red-500" />
                            )}
                            Code Change Analysis
                        </AlertDialogTitle>
                        <AlertDialogDescription className="prose max-w-none">
                            {analysisResult?.message && (
                                <div dangerouslySetInnerHTML={{ __html: marked.parse(analysisResult.message) }} />
                            )}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="flex gap-2">
                        {analysisResult?.analysis.recommendedAction === 'continue' && (
                            <AlertDialogAction asChild>
                                <Button className="bg-green-600 hover:bg-green-700">
                                    Continue Building
                                </Button>
                            </AlertDialogAction>
                        )}
                        {analysisResult?.analysis.recommendedAction === 'fix' && (
                            <>
                                <AlertDialogAction asChild>
                                    <Button variant="outline">Continue Anyway</Button>
                                </AlertDialogAction>
                                <AlertDialogAction asChild>
                                    <Button className="bg-amber-600 hover:bg-amber-700"
                                        onClick={() => {
                                            setInput(`Fix the following issues: ${analysisResult.analysis.potentialIssues
                                                .map(issue => issue.description)
                                                .join(', ')}`);
                                        }}>
                                        Fix Issues
                                    </Button>
                                </AlertDialogAction>
                            </>
                        )}
                        {analysisResult?.analysis.recommendedAction === 'redo' && (
                            <>
                                <AlertDialogAction asChild>
                                    <Button variant="outline">Continue Anyway</Button>
                                </AlertDialogAction>
                                <AlertDialogAction asChild>
                                    <Button className="bg-red-600 hover:bg-red-700"
                                        onClick={() => {
                                            setInput(`Please redo the implementation. The previous attempt had these issues: ${analysisResult.analysis.potentialIssues
                                                .map(issue => issue.description)
                                                .join(', ')}`);
                                        }}>
                                        Redo Implementation
                                    </Button>
                                </AlertDialogAction>
                            </>
                        )}
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
})

export default Chat;

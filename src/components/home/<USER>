'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wand2, ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import {useLocalStorage} from "usehooks-ts";
import MagicallyLogo from "@/components/logo";
import {useAuth} from "@/hooks/use-auth";
import IdeaForm from "@/components/home/<USER>";
import { trackIdeaFlowEvent } from "@/lib/analytics/track";
import _ from 'lodash';
import {Avatar, AvatarImage} from "@/components/ui/avatar";
import CreatorCount from "@/components/home/<USER>";

function AnimatedPromptPlaceholder() {
    const [currentPrompt, setCurrentPrompt] = useState(0);
    const [displayText, setDisplayText] = useState('');
    const [isTyping, setIsTyping] = useState(true);
    
    // Short, high-intent prompt examples
    const prompts = [
        "Create a fitness app with workout tracking and progress charts",
        "Build a recipe finder with ingredient search and meal planning",
        "Design a minimalist note-taking app with cloud sync",
        "Make a weather app with hourly forecasts and location-based alerts"
    ];
    
    // Typing animation effect
    useEffect(() => {
        if (!isTyping) return;
        
        const prompt = prompts[currentPrompt];
        let index = 0;
        
        const interval = setInterval(() => {
            if (index <= prompt.length) {
                setDisplayText(prompt.substring(0, index));
                index++;
            } else {
                clearInterval(interval);
                setIsTyping(false);
                
                // Pause before erasing
                setTimeout(() => {
                    setIsTyping(false);
                    
                    // Start erasing
                    let eraseIndex = prompt.length;
                    const eraseInterval = setInterval(() => {
                        if (eraseIndex >= 0) {
                            setDisplayText(prompt.substring(0, eraseIndex));
                            eraseIndex--;
                        } else {
                            clearInterval(eraseInterval);
                            
                            // Move to next prompt
                            setCurrentPrompt((prev) => (prev + 1) % prompts.length);
                            setIsTyping(true);
                        }
                    }, 30);
                }, 2000);
            }
        }, 50);
        
        return () => clearInterval(interval);
    }, [currentPrompt, isTyping]);
    
    return (
        <div className="text-muted-foreground">
            {displayText}<span className="animate-pulse">|</span>
        </div>
    );
}

export function HeroSection() {
    const [prompt, setPrompt] = useState('');
    
    // Track component mount
    useEffect(() => {
        trackIdeaFlowEvent('FORM_VIEWED', {
            source: 'home_page'
        });
    }, []);
    
    return (
        <div className="relative w-full overflow-hidden px-4">
            {/* Background elements - enhanced for glassmorphic effect */}
            <div className="absolute inset-0 bg-gradient-to-b from-background/50 to-background/20" />
            <div className="absolute top-20 -left-64 w-96 h-96 bg-primary/15 rounded-full blur-3xl opacity-30" />
            <div className="absolute bottom-20 -right-64 w-96 h-96 bg-accent/15 rounded-full blur-3xl opacity-30" />
            <div className="absolute top-40 right-20 w-72 h-72 bg-indigo-500/10 rounded-full blur-3xl" />
            <div className="absolute bottom-60 left-20 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl" />
            
            {/* Content with glassmorphic container */}
            <div className="relative mx-auto flex min-h-[90vh] max-w-5xl flex-col items-center justify-center px-1 py-20 text-center backdrop-blur-sm">
                {/* Logo with subtle glow effect */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="mb-16 relative"
                >
                   <div className="absolute inset-0 bg-primary/10 blur-xl rounded-full opacity-40" />
                   <MagicallyLogo className="relative z-10" logoWidthAction={50} />
                </motion.div>

                {/* Heading with more subtle gradient */}
                <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl mb-4 leading-tight"
                >
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary/90 via-primary/80 to-primary/90">
                        Build Mobile Apps
                    </span>{" "}
                    <span className="text-foreground">
                        in Minutes
                    </span>
                </motion.h1>

                {/* Professional subheading */}
                <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="text-xl text-muted-foreground max-w-2xl mb-10"
                >
                    Describe your app idea and magically will generate a professional,
                    fully-functional mobile application ready for App Stores.
                </motion.p>

                {/* Idea form with more subtle styling */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="w-full max-w-2xl mx-auto relative"
                >
                    <div className="absolute -inset-1 bg-gradient-to-r from-primary/15 via-accent/15 to-primary/15 rounded-xl blur opacity-30" />
                    <div className="relative">
                        <IdeaForm prompt={prompt} setPrompt={setPrompt} />
                    </div>
                </motion.div>

                {/* Professional indicators */}
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 text-sm text-muted-foreground"
                >
                    <CreatorCount/>
                    
                    <div className="flex items-center px-4 py-2 rounded-full bg-background/20 backdrop-blur-md border border-white/10 shadow-lg shadow-black/5">
                        <span className="mr-1 text-amber-400">⭐</span>
                        <span className="text-white/90">4.9/5 rating</span>
                    </div>

                    {/*<div className="px-4 py-2 rounded-full bg-background/20 backdrop-blur-md border border-white/10 shadow-lg shadow-black/5">*/}
                    {/*    <span className="text-white/90">No credit card required</span>*/}
                    {/*</div>*/}
                </motion.div>

                {/*/!* App suggestions with subtle styling *!/*/}
                {/*<motion.div*/}
                {/*    initial={{ opacity: 0 }}*/}
                {/*    animate={{ opacity: 1 }}*/}
                {/*    transition={{ duration: 0.5, delay: 0.5 }}*/}
                {/*    className="mt-10 w-full"*/}
                {/*>*/}
                {/*    <div className="mb-3 text-sm text-center text-muted-foreground">Popular app ideas to get started</div>*/}
                {/*    <div className="flex flex-wrap justify-center gap-2 max-w-3xl mx-auto">*/}
                {/*        {[*/}
                {/*            { id: '1', icon: '🧘', title: 'Wellness', prompt: 'Create a wellness app with personalized meditation and mood tracking' },*/}
                {/*            { id: '2', icon: '🏠', title: 'Smart Home', prompt: 'Build a smart home dashboard with energy usage analytics' },*/}
                {/*            { id: '3', icon: '👔', title: 'Networking', prompt: 'Design a professional networking app with AI-powered connection suggestions' },*/}
                {/*            { id: '4', icon: '💹', title: 'Finance', prompt: 'Make a personal finance app with investment portfolio visualization' },*/}
                {/*            { id: '5', icon: '🎨', title: 'Design', prompt: 'Create a collaborative design tool with real-time feedback' },*/}
                {/*            { id: '6', icon: '🌍', title: 'Travel', prompt: 'Build a travel planning app with local experiences and personalized itineraries' },*/}
                {/*        ].map((item) => (*/}
                {/*            <motion.button*/}
                {/*                key={item.id}*/}
                {/*                className="flex items-center gap-2 py-2 px-4 rounded-full bg-background/30 backdrop-blur-sm border border-border/30 hover:bg-background/50 text-foreground"*/}
                {/*                whileHover={{ scale: 1.02 }}*/}
                {/*                whileTap={{ scale: 0.98 }}*/}
                {/*                onClick={() => setPrompt(item.prompt)}*/}
                {/*            >*/}
                {/*                <span>{item.icon}</span>*/}
                {/*                <span>{item.title}</span>*/}
                {/*            </motion.button>*/}
                {/*        ))}*/}
                {/*    </div>*/}
                {/*</motion.div>*/}
            </div>
        </div>
    );
}

type AppSuggestionProps = {
    suggestions: Array<{
        title: string;
        prompt: string;
        category: string;
        icon: string;
    }>;
    randomSuggestions: Array<{
        id: string;
        icon: string;
        title: string;
        prompt: string;
    }>;
    showAllOptions: boolean;
    setShowAllOptions: (show: boolean) => void;
    setPrompt: (prompt: string) => void;
};

const SuggestionButton = ({ item, index, onClick }: { item: string; index: number; onClick: () => void }) => {
    const [icon, ...titleParts] = item.split(' ');
    const title = titleParts.join(' ');
    
    return (
        <motion.button
            key={`suggestion-${index}`}
            className="flex items-center py-1 px-3 rounded-full bg-background border border-border hover:bg-secondary/10 text-foreground whitespace-nowrap"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{
                duration: 0.2,
                delay: 0.05 + (index % 8) * 0.03,
                ease: 'easeOut'
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onClick}
        >
            <span className="mr-1.5">{icon}</span>
            <span className="text-xs">{title}</span>
        </motion.button>
    );
};

function AppSuggestions({ suggestions, randomSuggestions, showAllOptions, setShowAllOptions, setPrompt }: AppSuggestionProps) {
    // Map all suggestions to their full data
    const allSuggestionItems = suggestions.map(s => ({
        id: `${s.icon} ${s.title}`,
        icon: s.icon,
        title: s.title,
        prompt: s.prompt
    }));
    
    // Create a map for fast lookup of random suggestions
    const randomSuggestionsMap = randomSuggestions.reduce((acc, item) => {
        acc[item.id] = true;
        return acc;
    }, {} as Record<string, boolean>);
    
    // Sort all suggestions so random ones appear first in the same order
    const sortedItems = [...allSuggestionItems].sort((a, b) => {
        const aIsRandom = randomSuggestionsMap[a.id];
        const bIsRandom = randomSuggestionsMap[b.id];
        
        if (aIsRandom && !bIsRandom) return -1;
        if (!aIsRandom && bIsRandom) return 1;
        
        if (aIsRandom && bIsRandom) {
            // Maintain the same order as in randomSuggestions
            const aIndex = randomSuggestions.findIndex(item => item.id === a.id);
            const bIndex = randomSuggestions.findIndex(item => item.id === b.id);
            return aIndex - bIndex;
        }
        
        return 0;
    });
    
    // Use either all sorted suggestions or just the random ones
    const displayItems = showAllOptions ? sortedItems : randomSuggestions;
    
    // Handle suggestion click
    const handleSuggestionClick = (prompt: string) => {
        setPrompt(prompt);
        
        // Focus the textarea to draw attention to it
        setTimeout(() => {
            const textarea = document.querySelector('textarea');
            if (textarea) textarea.focus();
        }, 100);
        
        // Optional: auto-submit the form
        // const form = document.querySelector('form');
        // if (form) form.dispatchEvent(new Event('submit', { cancelable: true }));
    };
    
    return (
        <div className="relative flex justify-center max-w-2xl mx-auto">
            <div className="flex flex-wrap justify-center gap-x-2 gap-y-2 w-full">
                {/* Display either all suggestions or just the random ones */}
                {displayItems.map((item, index) => (
                    <motion.button
                        key={item.id}
                        className="flex items-center py-1 px-3 rounded-full bg-background border border-border hover:bg-secondary/10 text-foreground whitespace-nowrap"
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{
                            duration: 0.2,
                            delay: 0.05 + (index % 8) * 0.03,
                            ease: 'easeOut'
                        }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleSuggestionClick(item.prompt)}
                    >
                        <span className="mr-1.5">{item.icon}</span>
                        <span className="text-xs">{item.title}</span>
                    </motion.button>
                ))}
                
                {/* Show less button when all options are visible */}
                {showAllOptions && (
                    <motion.button
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="mt-2 flex items-center justify-center py-1 px-4 rounded-full bg-secondary/20 hover:bg-secondary/30 text-foreground whitespace-nowrap"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowAllOptions(false)}
                    >
                        <span className="text-xs">Show less</span>
                    </motion.button>
                )}
                
                {/* More button with shadow overlay */}
                {!showAllOptions && (
                    <div className="absolute bottom-0 left-0 right-0 flex justify-center items-end pb-1">
                        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-background to-transparent pointer-events-none"></div>
                        <motion.button
                            className="relative z-10 flex items-center justify-center py-1 px-4 rounded-full bg-secondary/20 hover:bg-secondary/30 text-foreground whitespace-nowrap"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setShowAllOptions(true)}
                        >
                            <span className="text-xs mr-1">More options</span>
                            <ChevronDown className="h-3 w-3" />
                        </motion.button>
                    </div>
                )}
            </div>
        </div>
    );
}
